<template>
    <!--
        组件：创建付款审批单
        路径：src\views\mach\pm\flowContract\flowApvContractPayInfo\apvContractPayInfo-form.vue
    -->
    <basic-container>
        <el-row style="margin:0 auto 10px auto;" >
            <avue-form :option="payinfoOption" v-model="form" ref="payInfoForm">
                <!-- 请款单列表插槽 -->
                <template #paymentRealList>
                    <div class="payment-real-container">
                        <div class="upload-excel-container">
                            <el-upload
                                class="excel-upload"
                                action="#"
                                :auto-upload="false"
                                :show-file-list="true"
                                :on-change="handleExcelChange"
                                accept=".xlsx, .xls">
                                <el-button type="primary">
                                    <i class="el-icon-upload"></i> 上传Excel导入请款单
                                </el-button>
                            </el-upload>
                            <div class="excel-tip">
                                <p>* 支持 .xlsx, .xls 格式的Excel文件</p>
                                <p>* Excel文件应包含以下列：付款方、收款方、合同编号、单套产品合同总额、单套产品付款金额、备注</p>
                            </div>
                        </div>

                        <avue-crud
                            ref="paymentRealCrud"
                            v-model="paymentRealForm"
                            :option="paymentRealOption"
                            :data="paymentRealList"
                            :page="paymentRealPage"
                            @update:page="handlePaymentRealPageUpdate"
                            @on-load="onPaymentRealLoad"
                            @row-update="handlePaymentRealUpdate"
                            @row-save="handlePaymentRealSave"
                            @row-del="handlePaymentRealDelete">
                            <!-- 移除自定义保存按钮，使用Avue标准的行编辑模式 -->

                            <!-- 合同上传插槽 -->
                            <template #contractFiles="scope">
                                <mach-upload
                                    v-model:value="scope.row.contractFiles"
                                    :fileSize="20"
                                    :limit="1"
                                    :disabled="false"
                                    :file-type="['pdf']"
                                    :downloadBtn="true"
                                    :previewBtn="true">
                                </mach-upload>
                            </template>

                            <!-- 附件上传插槽 -->
                            <template #attachmentFiles="scope">
                                <mach-upload
                                    v-model:value="scope.row.attachmentFiles"
                                    :fileSize="20"
                                    :limit="5"
                                    :disabled="false"
                                    :file-type="['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx']"
                                    :downloadBtn="true"
                                    :previewBtn="true">
                                </mach-upload>
                            </template>
                        </avue-crud>

                        <div v-if="paymentRealList.length > 0" class="payment-real-summary">
                            <p>共 <span class="highlight">{{paymentRealList.length}}</span> 条请款单数据</p>
                            <p>付款金额合计: <span class="highlight">{{getTotalPaymentAmount()}}</span> 元</p>
                        </div>
                    </div>
                </template>

                <!-- 附件上传插槽 -->
                <template #attachments>
                    <mach-upload v-model:value="form.attachments" :fileSize="20"
                        :limit="5" :disabled="false"
                        :file-type="['pdf']"
                        :downloadBtn="true"
                        :previewBtn="true">
                    </mach-upload>
                </template>
            </avue-form>
        </el-row>



        <!-- 选择供应商 -->
        <el-dialog
          title="选择供应商"
          append-to-body
          v-model="selectSupplierBox.open"
          destroy-on-close
          width="90%"
          top="1%"
          :close-on-click-modal="false"
          :before-close="() => { selectSupplierBox.open = false; }">
            <supplierSelectVue @onSelected="selectSupplierBox.onSelected" @handleCancel="()=>{selectSupplierBox.open = false;}" />
        </el-dialog>

        <el-row style="margin:10px 0 0 0;justify-content: center;" v-if="showBtns">
              <el-button type="primary" icon="Check" @click="handleSubmit" :disabled="loading" :loading="submitLoading">提交</el-button>
              <el-button icon="Close" @click="handleCancel(false)">关闭</el-button>
        </el-row>

    </basic-container>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, watch, nextTick } from 'vue';
import machUpload from "@/components/mach/upload/mach-upload.vue";
import { submit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
import supplierSelectVue from "./supplier-select.vue";
import { getDictionary as getDictionaryBiz } from "@/api/system/dictbiz";
import { getSupplierSelect } from "@/api/mach/basis/supplier";
import { importExcel, save, updatePaymentReal, removePaymentReal } from "@/api/mach/pm/flowApvContractPayPaymentReal/apvContractPayPaymentReal";


  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    flowApvContractPayInfo:{},//修改时传过来的付款单信息
    partyA: {},//甲方，付款方
  });

  //对外暴露方法
  defineExpose({validForm});

  const loading = ref(false);
  const submitLoading = ref(false);
  const form = ref({
    payDetailList: [], // 初始化支付方式列表
    attachments: '', // 初始化附件字段
    paymentRealList: [] // 初始化请款单列表
  });
  const showBtns = ref(true);
  const yesNoDic = ref([]);

  // 请款单相关数据
  const paymentRealList = ref([]);
  const paymentRealForm = ref({});
  const paymentRealPage = ref({
    total: 0,
    currentPage: 1,
    pageSize: 10
  });
  const currentPaymentReal = ref({});

  // 请款单表格配置(使用avue行编辑标准模式)
  const paymentRealOption = ref({
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    searchMenuSpan: 6,
    rowKey: 'id', // 指定行数据的唯一标识字段
    addRowBtn: true, // 显示行内新增按钮
    addBtn: false, // 禁用顶部新增按钮
    cellBtn: true, // 启用单元格编辑按钮
    editBtn: true, // 启用编辑按钮
    saveAutoRefresh: true, // 启用保存后自动刷新表格
    updateBtn: true, // 启用更新按钮
    viewBtn: false, // 禁用查看按钮
    delBtn: true, // 显示删除按钮
    cell: true, // 启用行内编辑
    refreshBtn: true, // 添加刷新按钮
    columnBtn: false, // 禁用列显示按钮
    menu: true, // 显示操作菜单
    menuWidth: 300, // 设置操作列宽度
    rowAdd: (done) => {
      const newRow = {
        // 不设置id，这样新增行会显示"保存"按钮
        partyA: form.value.partyA || '',
        partyB: form.value.partyB || '',
        contractCode: '',
        eqAmount: 0,
        eqPaymentAmount: 0,
        remarks: '',
        contractFiles: '',
        attachmentFiles: ''
      };
      paymentRealList.value.push(newRow);
      done(newRow); // 必须调用done完成新增
    },
    column: [
      {
        label: '收款方',
        prop: 'partyB',
        minWidth: 100,
        cell: true,
        editDisabled: false,
        rules: [{ required: true, message: '必填项', trigger: 'blur' }]
      },
      {
        label: '合同编号',
        prop: 'contractCode',
        minWidth: 100,
        cell: true,
        editDisabled: false,
        rules: [{ required: true, message: '必填项', trigger: 'blur' }]
      },
      {
        label: '合同总额',
        prop: 'eqAmount',
        minWidth: 120,
        type: 'number',
        cell: true,
        precision: 2,
        editDisabled: false,
        rules: [
          { required: true, message: '必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '必须大于等于0' }
        ]
      },
      {
        label: '付款金额',
        prop: 'eqPaymentAmount',
        minWidth: 120,
        type: 'number',
        cell: true,
        precision: 2,
        editDisabled: false,
        rules: [
          { required: true, message: '必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '必须大于等于0' }
        ]
      },
      {
        label: '备注',
        prop: 'remarks',
        minWidth: 100,
        cell: true,
        editDisabled: false
      },
      {
        label: '合同',
        prop: 'contractFiles',
        minWidth: 180,
        type: 'upload',
        listType: 'text',
        slot: true,
        formslot: true,
        limit: 1,
        fileSize: 20,
        propsHttp: {
          res: 'data'
        },
        accept: '.pdf',
        showFileList: true,
        overHidden: true,
        tooltip: true
      },
      {
        label: '附件',
        prop: 'attachmentFiles',
        minWidth: 180,
        type: 'upload',
        listType: 'text',
        slot: true,
        formslot: true,
        limit: 5,
        fileSize: 20,
        propsHttp: {
          res: 'data'
        },
        accept: '.pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx',
        showFileList: true,
        overHidden: true,
        tooltip: true
      }
    ]
  });

  // 处理Excel文件上传
  const handleExcelChange = async (file) => {
    if (!file) return;

    // 检查文件类型
    const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase());
    if (!isExcel) {
      proxy.$message.error('请上传 Excel 文件（.xlsx 或 .xls 格式）');
      return;
    }

    // 检查必填字段
    if (!form.value.partyA) {
      proxy.$message.error('请先选择甲方（付款方）');
      return;
    }

    loading.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.raw);
      formData.append('partyA', form.value.partyA); // 添加付款方信息
      formData.append('partyB', form.value.partyB); // 添加收款方信息

      const res = await importExcel(formData);
      if (res.data) {
        // 处理导入的数据，确保数据格式正确
        const importedData = res.data.map(item => ({
          ...item,
          contractFiles: '',
          attachmentFiles: '',
          _imported: true, // 标记为导入数据
          _saved: false // 标记为未保存
        }));

        // 验证导入的数据
        const invalidData = importedData.filter(item =>
          !item.contractCode ||
          !item.partyB ||
          item.eqAmount === undefined ||
          item.eqAmount < 0 ||
          item.eqPaymentAmount === undefined ||
          item.eqPaymentAmount < 0
        );

        if (invalidData.length > 0) {
          proxy.$message.error(`导入数据中有 ${invalidData.length} 条无效数据，请检查必填字段和金额`);
          return;
        }

        paymentRealList.value = importedData;
        form.value.paymentRealList = importedData;

        // 更新表单的付款金额
        const total = getTotalPaymentAmount();
        form.value.amount = total;

        // 提示用户需要保存数据
        proxy.$message({
          message: `成功导入 ${importedData.length} 条请款单数据，请先保存数据后再上传文件`,
          type: 'success',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Excel导入失败:', error);
      proxy.$message.error(error.message || 'Excel导入失败，请检查文件格式是否正确');
    } finally {
      loading.value = false;
    }
  };

  // 保存单行数据
  const handleSaveRow = async (row, index) => {
    if (loading.value) return;
    loading.value = true;
    debugger;
    try {
      // 验证必填字段
      if (!row.partyB) {
        throw new Error('收款方不能为空');
      }
      if (!row.contractCode) {
        throw new Error('合同编号不能为空');
      }
      if (!row.eqAmount || row.eqAmount < 0) {
        throw new Error('合同总额必须大于等于0');
      }

      // 准备保存的数据
      const saveData = {
        id: row.id ? String(row.id) : undefined, // 显式处理id字段
        apvContractPayId: form.value.id || '', // 添加付款单ID
        partyA: row.partyA || form.value.partyA,
        partyB: row.partyB || form.value.payee,
        contractCode: row.contractCode,
        eqAmount: row.eqAmount,
        eqPaymentAmount: row.eqPaymentAmount,
        remarks: row.remarks,
        contractFiles: Array.isArray(row.contractFiles) ? row.contractFiles.join(',') : (row.contractFiles || ''),
        attachmentFiles: Array.isArray(row.attachmentFiles) ? row.attachmentFiles.join(',') : (row.attachmentFiles || '')
      };

      // 如果是新增，确保没有id字段
      if (!saveData.id) {
        delete saveData.id;
      }

      console.log('保存请款单数据:', saveData);

      // 根据是否有ID决定是新增还是更新
      const res = saveData.id ? await updatePaymentReal(saveData) : await save(saveData);

      if (res && res.data) {
        let newId = '';

        // 从响应中获取ID
        if (res.data && typeof res.data === 'object') {
          if (res.data.data && typeof res.data.data === 'object') {
            newId = res.data.data.data?.id || '';
            console.log('从res.data.data.data中获取ID:', newId);
          } else {
            newId = res.data.data?.id || res.data.id || '';
            console.log('从res.data.data或res.data中获取ID:', newId);
          }
        } else {
          newId = res.data || '';
          console.log('从res.data直接获取ID:', newId);
        }

        // 确保newId不为undefined
        newId = newId || '';
        newId = String(newId);
        console.log('最终使用的ID:', newId);

        // 创建新的响应式对象
        const savedData = {
          ...row,
          id: newId,
          contractFiles: row.contractFiles || '',
          attachmentFiles: row.attachmentFiles || '',
          _imported: row._imported || false,
          _saved: true
        };

        console.log('保存前的行数据:', row);
        console.log('更新后的数据:', savedData);

        // 更新本地数据
        await nextTick();
        const index = paymentRealList.value.findIndex(item => item === row);
        if (index !== -1) {
          paymentRealList.value.splice(index, 1, savedData);

          // 强制更新表单中的请款单列表
          form.value.paymentRealList = [...paymentRealList.value];

          console.log('更新后的列表数据:', paymentRealList.value);
          console.log('当前行的ID:', paymentRealList.value[index].id);

          // 更新付款金额总计
          const total = getTotalPaymentAmount();
          form.value.amount = total;

          // 强制更新Avue表格数据
          proxy.$refs.paymentRealCrud.setList(paymentRealList.value);

          proxy.$message.success('保存成功，现在可以上传文件了');
        } else {
          console.error('未找到要更新的行数据');
          proxy.$message.error('保存失败：未找到要更新的行数据');
        }
      } else {
        throw new Error(res?.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存行数据失败:', error);
      proxy.$message.error(error.message || '保存失败');
    } finally {
      loading.value = false;
    }
  };




  // 请款单列表分页加载
  const onPaymentRealLoad = (page) => {
    paymentRealPage.value = page;
  };

  // 处理请款单列表分页加载
  const handlePaymentRealPageUpdate = (newPage) => {
    paymentRealPage.value = newPage;
  };


  // 处理请款单行更新（避免递归更新）
  const handlePaymentRealUpdate = async (row, index, done, loading) => {
    console.log('处理请款单行更新:', row, index);
    try {
      // 确保ID存在且为字符串类型
      if (!row.id) {
        proxy.$message.error('更新数据缺少ID，无法更新');
        done(false);
        return;
      }

      // 准备更新数据
      const updateData = {
        id: row.id ? String(row.id) : undefined, // 显式处理id字段
        apvContractPayId: form.value.id || '', // 添加付款单ID
        partyA: row.partyA || form.value.partyA,
        partyB: row.partyB || form.value.payee,
        contractCode: row.contractCode,
        eqAmount: row.eqAmount,
        eqPaymentAmount: row.eqPaymentAmount,
        remarks: row.remarks,
        contractFiles: Array.isArray(row.contractFiles) ? row.contractFiles.join(',') : (row.contractFiles || ''),
        attachmentFiles: Array.isArray(row.attachmentFiles) ? row.attachmentFiles.join(',') : (row.attachmentFiles || '')
      };

      console.log('更新请款单数据:', updateData);

      // // 模拟网络请求
      // proxy.$message.success('模拟网络请求');

      // 先关闭按钮等待
      setTimeout(() => {
        proxy.$message.success('关闭按钮等待');
        loading(); // 关闭按钮等待状态
      }, 1000);

      // 然后执行实际的更新操作
      setTimeout(async () => {
        try {
          const res = await updatePaymentReal(updateData); // 使用更新接口
          if (res && res.data) {
            // 获取返回的ID
            let newId;
            if (typeof res.data === 'object' && res.data !== null) {
              // 尝试多种可能的ID路径，如果都没有则使用原ID
              newId = res.data.data?.id || res.data.id || res.data || row.id;
            } else {
              newId = res.data || row.id;
            }

            // 确保ID是字符串类型
            newId = String(newId || row.id);

            // 更新本地数据
            const updatedRow = {
              ...updateData,
              id: newId, // 确保ID正确更新
              _saved: true // 标记为已保存
            };

            console.log('更新成功，ID:', newId, '更新的行数据:', updatedRow);

            // 更新列表数据
            paymentRealList.value[index] = updatedRow;

            // 强制更新表单中的请款单列表
            form.value.paymentRealList = [...paymentRealList.value];

            // 更新付款金额总计
            const total = getTotalPaymentAmount();
            form.value.amount = total;

            // 完成编辑
            proxy.$message.success('编辑数据' + JSON.stringify(updatedRow) + '数据序号' + index);
            done();
          } else {
            proxy.$message.error(res?.msg || '修改失败');
            done(false);
          }
        } catch (error) {
          console.error('更新请款单失败:', error);
          proxy.$message.error('修改失败');
          done(false);
        }
      }, 2000);
    } catch (error) {
      console.error('更新请款单失败:', error);
      proxy.$message.error('修改失败');
      done(false);
    }
  };

  // 处理请款单行保存
  const handlePaymentRealSave = (row, done) => {
    console.log('新增数据', JSON.stringify(row));
    // 准备保存的数据
    const saveData = {
      apvContractPayId: form.value.id || '', // 添加付款单ID
      partyA: row.partyA || form.value.partyA,
      partyB: row.partyB || form.value.payee,
      contractCode: row.contractCode,
      eqAmount: row.eqAmount,
      eqPaymentAmount: row.eqPaymentAmount,
      remarks: row.remarks,
      contractFiles: Array.isArray(row.contractFiles) ? row.contractFiles.join(',') : (row.contractFiles || ''),
      attachmentFiles: Array.isArray(row.attachmentFiles) ? row.attachmentFiles.join(',') : (row.attachmentFiles || '')
    };

    // 调用保存API
    save(saveData).then(res => {
      if (res && res.data) {
        // 获取返回的ID
        let newId = '';
        if (typeof res.data === 'object' && res.data !== null) {
          newId = res.data.data?.id || res.data.id || res.data || '';
        } else {
          newId = res.data || '';
        }

        // 确保ID是字符串类型
        newId = String(newId || '');

        // 更新本地数据
        row.id = newId;
        row._saved = true;

        // 更新付款金额总计
        const total = getTotalPaymentAmount();
        form.value.amount = total;

        proxy.$message.success('新增数据' + JSON.stringify(row));
        done();
      } else {
        proxy.$message.error(res?.msg || '保存失败');
        done(false);
      }
    }).catch(error => {
      console.error('新增请款单失败:', error);
      proxy.$message.error('保存失败');
      done(false);
    });
  };

  // 处理请款单行删除
  const handlePaymentRealDelete = async (row, index, done) => {
    try {
      // // 模拟网络请求
      // proxy.$message.success('模拟网络请求');

      // 延迟执行删除操作
      setTimeout(async () => {
        try {
          if (row.id) {
            const res = await removePaymentReal({ ids: row.id });

            if (res.data.success) {
              paymentRealList.value.splice(index, 1);
              form.value.paymentRealList = [...paymentRealList.value];

              // 更新付款金额总计
              const total = getTotalPaymentAmount();
              form.value.amount = total;

              proxy.$message.success('删除数据' + JSON.stringify(row) + '数据序号' + index);
              done();
            } else {
              proxy.$message.error(res.data.msg || '删除失败');
              done(false);
            }
          } else {
            paymentRealList.value.splice(index, 1);
            form.value.paymentRealList = [...paymentRealList.value];

            // 更新付款金额总计
            const total = getTotalPaymentAmount();
            form.value.amount = total;

            proxy.$message.success('删除数据' + JSON.stringify(row) + '数据序号' + index);
            done();
          }
        } catch (error) {
          console.error('删除请款单失败:', error);
          proxy.$message.error('删除失败');
          done(false);
        }
      }, 1000);
    } catch (error) {
      console.error('删除请款单失败:', error);
      proxy.$message.error('删除失败');
      done(false);
    }
  };

  // 计算请款单总金额
  const getTotalPaymentAmount = () => {
    return paymentRealList.value.reduce((total, item) => {
      return total + Number(item.eqPaymentAmount || 0);
    }, 0).toFixed(2);
  };

  // 手动更新付款金额
  const updatePaymentAmount = () => {
    if (paymentRealList.value && paymentRealList.value.length > 0) {
      const total = getTotalPaymentAmount();
      form.value.amount = total;
      proxy.$message.success(`请款单总金额已更新为: ${total} 元`);
    }
  };





  // 付款单-表单
  const payinfoOption = ref({
    tip: false,
    border: true,
    index: true,
    submitBtn: false,
    addbtn: false,
    emptyBtn: false,
    viewBtn: false,
    delBtn: false,
    editBtn: false,
    gridBtn: false,
    columnBtn: false,
    labelWidth: 140,
    dialogClickModal: false,
    column: [
      {
        label: '编号',
        prop: 'code',
        type: 'input',
        disabled: true,
        row: true,
        rules: [
          {
            required: true,
            message: "请输入编号",
            trigger: "blur"
          }
        ]
      },
      {
        label: '合同类型',
        prop: 'type',
        type: 'select',
        dicData: [
          {
            label: "采购合同",
            value: "1"
          },
          {
            label: "安装合同",
            value: "2"
          }
        ],
        rules: [
          {
            required: true,
            message: "请选择合同类型",
            trigger: "blur"
          }
        ]
      },
      {
        label: "甲方",
        prop: "partyA",
        type: "select",
        dicData: [
          {
            label: "北京",
            value: "3"
          },
          {
            label: "江苏",
            value: "2"
          },
          {
            label: "天津",
            value: "1"
          }
        ],
        rules: [
          {
            required: true,
            message: "请选择甲方",
            trigger: "blur"
          }
        ]
      },
      {
        label: '收款单位',
        prop: 'payee',
        minWidth: 200,
        type: 'select',
        dicData: [],
        props: {
          label: 'name',
          value: 'name'
        },
        filterable: true,
        remote: true,
        reserveKeyword: true,
        suffixIcon: 'el-icon-search',
        placeholder: "请输入关键字搜索收款单位",
        prefixIcon: 'el-icon-search',
        suffixClick: () => {
          selectSupplierBox.open = true;
          selectSupplierBox.onSelected = handleSupplierSelect;
        },
        remoteMethod: async (query) => {
          if (query) {
            const suppliers = await getSupplierList(query);
            const payeeOpt = proxy.findObject(payinfoOption.value.column, "payee");
            if (payeeOpt) {
              payeeOpt.dicData = suppliers;
            }
          }
        },
        change: ({value, column, dic, item}) => {
          console.log("收款单位选择事件:", {value, column, dic, item});

          // 从dic中查找匹配的供应商数据
          let supplier = null;
          if (dic && dic.length > 0) {
            supplier = dic.find(d => d.name === value);
          }

          // 如果没有找到，尝试从全局存储的供应商数据中查找
          if (!supplier && window.supplierData) {
            supplier = window.supplierData.find(d => d.name === value);
          }

          // 更新表单数据
          if (supplier) {
            console.log("找到供应商数据:", supplier);
            form.value.payeeSimple = supplier.shortName || '';
            form.value.bankAddress = supplier.bankName || '';
            form.value.bankAccount = supplier.bankAccount || '';
            console.log("已填充供应商相关字段:", form.value);
          } else {
            console.warn("未找到匹配的供应商数据");
          }
        },
        rules: [
          {
            required: true,
            message: "请选择收款单位",
            trigger: "blur"
          }
        ]
      },
      {
        label: '单位简称',
        prop: 'payeeSimple',
        minWidth: 180,
        readonly: true,
        placeholder: "选择收款单位后自动填充",
        rules: [
          {
            required: true,
            message: "请选择收款单位",
            trigger: "blur"
          }
        ]
      },
      {
        label: "紧急程度",
        prop: "level",
        type: "select",
        minWidth: 160,
        dicData: [
          {
            label: "一般",
            value: "1"
          },
          {
            label: "紧急",
            value: "2"
          },
          {
            label: "特急",
            value: "3"
          }
        ],
        rules: [
          {
            required: true,
            message: "请选择紧急程度",
            trigger: "blur"
          }
        ]
      },
      {
        label: '款项用途',
        prop: 'fundsUse',
        minWidth: 160,
        maxlength: 15,
        rules: [
          {
            required: true,
            message: "请输入款项用途",
            trigger: "blur"
          }
        ]
      },
      {
        label: '开户行',
        prop: 'bankAddress',
        minWidth: 160,
        readonly: true,
        placeholder: "选择收款单位后自动填充",
        rules: [
          {
            required: true,
            message: "请选择收款单位",
            trigger: "blur"
          }
        ]
      },
      {
        label: '账号',
        prop: 'bankAccount',
        minWidth: 160,
        readonly: true,
        placeholder: "选择收款单位后自动填充",
        rules: [
          {
            required: true,
            message: "请选择收款单位",
            trigger: "blur"
          }
        ]
      },
      {
        label: '付款金额',
        prop: 'amount',
        minWidth: 160,
        type: "number",
        precision: 2,
        rules: [
          {
            required: true,
            message: "请输入付款金额",
            trigger: "blur"
          }
        ],
        change: ({value}) => {
          const newAmount = Number(value || 0);

          // 如果付款金额有效
          if (newAmount > 0) {
            // 如果已有支付方式，则调整支付方式金额
            if (form.value.payDetailList && form.value.payDetailList.length > 0) {
              const totalPaymentAmount = Number(payTypeTotalAmount.value);

              if (Math.abs(totalPaymentAmount - newAmount) > 0.01) {
                // 如果只有一种支付方式，直接更新其金额
                if (form.value.payDetailList.length === 1) {
                  form.value.payDetailList[0].amount = newAmount;
                } else {
                  // 如果有多种支付方式，调整最后一个支付方式的金额
                  const lastIndex = form.value.payDetailList.length - 1;
                  const otherAmount = totalPaymentAmount - Number(form.value.payDetailList[lastIndex].amount || 0);
                  const remainingAmount = newAmount - otherAmount;

                  if (remainingAmount > 0) {
                    form.value.payDetailList[lastIndex].amount = remainingAmount;
                  } else {
                    // 如果剩余金额小于等于0，提示用户
                    proxy.$Message.warning("付款金额小于已有支付方式金额总和，请调整支付方式金额");
                  }
                }
              }
            }
            // 如果没有支付方式，则自动添加一个
            else {
              // 获取支付方式字段配置
              let payDetailListOpt = proxy.findObject(payinfoOption.value.column, "payDetailList");
              if (payDetailListOpt) {
                payDetailListOpt.children.rowAdd((pd) => {
                  pd.amount = newAmount;
                  form.value.payDetailList = [pd];
                });
              }
            }
          }
        }
      },
      {
        label: '支出说明',
        prop: 'payDesc',
        type: 'textarea',
        minWidth: 160,
        minRows: 3,
        maxRows: 5,
        rules: [
          {
            required: true,
            message: "请输入支出说明",
            trigger: "blur"
          }
        ]
      },
      {
        label: '银行信息变更',
        prop: 'bankChange',
        minWidth: 160,
        type: "select",
        dicData: yesNoDic,
        props: {
          label: "dictValue",
          value: "dictKey"
        },
        value: '0',
        rules: [
          {
            required: true,
            message: '必选项',
            trigger: 'blur',
          }
        ],
      },
      {
        label: '是否开具发票',
        prop: 'invoiceStatus',
        minWidth: 160,
        type: "select",
        dicData: yesNoDic,
        props: {
          label: "dictValue",
          value: "dictKey"
        },
        value: '0',
        rules: [
          {
            required: true,
            message: '必选项',
            trigger: 'blur'
          }
        ],
      },
      {
        label: '发票备注',
        prop: 'invoiceRemark',
        minWidth: 160,
        display: false,
      },
      {
        label: '货物是否入库',
        prop: 'instoreStatus',
        minWidth: 160,
        type: "select",
        dicData: yesNoDic,
        props: {
          label: "dictValue",
          value: "dictKey"
        },
        value: '0',
        rules: [
          {
            required: true,
            message: '必选项',
            trigger: 'blur',
          }
        ],
      },
      {
        label: '入库备注',
        prop: 'instoreRemark',
        minWidth: 160,
        display: false,
      },
      {
        label: '备注信息',
        prop: 'remarks',
        type: 'textarea',
        minWidth: 160,
        maxlength: 200,
      },
      {
        label: '请款单列表',
        prop: 'paymentRealList',
        type: 'input',
        span: 24,
        slot: true,
        minWidth: 160,
      },
      // {
      //   label: '附件',
      //   prop: 'attachments',
      //   type: 'input',
      //   span: 24,
      //   slot: true,
      //   minWidth: 160,
      //   rules: [
      //     {
      //       required: true,
      //       message: '必选项',
      //       trigger: 'blur',
      //     }
      //   ],
      // },
      // 支付方式
      {
        label: '支付方式',
        prop: 'payDetailList',
        minWidth: 160,
        span: 24,
        type: 'dynamic',
        value: [],
        children: {
          align: 'center',
          headerAlign: 'center',
          showOverflowTooltip: true,
          rowAdd: (done) => {
            // 获取最大排序
            let maxSort = 0;
            if(form.value.payDetailList && form.value.payDetailList.length > 0){
              maxSort = Math.max(...form.value.payDetailList.map((item)=>{
                return parseInt(item.sorts || 0);
              }));
            }

            // 检查是否已填写付款金额
            if (!form.value.amount || Number(form.value.amount) <= 0) {
              proxy.$Message.warning("请先填写付款金额");
              return;
            }

            // 计算剩余可添加金额
            const totalAmount = Number(form.value.amount || 0);
            const currentTotal = Number(payTypeTotalAmount.value);
            const remainingAmount = totalAmount - currentTotal;

            if (remainingAmount <= 0) {
              proxy.$Message.warning("支付方式金额已等于付款金额，无需再添加支付方式");
              return;
            }

            done({
              type: "2", // 默认银行转账
              amount: remainingAmount,
              sorts: maxSort + 1,
              remarks: ''
            });
          },
          rowDel: (row, done) => {
            // 当删除支付方式时，如果还有其他支付方式，则调整最后一个支付方式的金额
            const payDetailList = form.value.payDetailList;
            const index = payDetailList.findIndex(item => item === row);

            if (index !== -1) {
              // 如果删除的不是最后一个支付方式，且有多个支付方式
              if (payDetailList.length > 1 && index !== payDetailList.length - 1) {
                const deletedAmount = Number(row.amount || 0);
                const lastIndex = payDetailList.length - 1;

                // 将删除的金额添加到最后一个支付方式
                payDetailList[lastIndex].amount = Number(payDetailList[lastIndex].amount || 0) + deletedAmount;
              }
            }

            done();
          },
          column: [{
            label: '支付方式',
            prop: "type",
            type: 'select',
            dicData: [
              {
                label: "现金",
                value: "1"
              },
              {
                label: "银行转账",
                value: "2"
              },
              {
                label: "支票",
                value: "3"
              }
            ],
            rules: [
              {
                required: true,
                message: '请选择支付方式',
                trigger: 'blur',
              },
            ],
          },{
            minWidth: 160,
            label: '金额（元）',
            prepend: '￥',
            prop: "amount",
            type: "number",
            precision: 2,
            rules: [
              {
                required: true,
                message: '请输入金额',
                trigger: 'blur',
              },
            ],
            change: ({value, row, index}) => {
              // 当支付方式金额变化时，自动调整最后一个支付方式的金额
              if (!form.value.amount || Number(form.value.amount) <= 0) {
                return;
              }

              // 如果不是最后一个支付方式，且有多个支付方式，则调整最后一个支付方式的金额
              const payDetailList = form.value.payDetailList;
              if (payDetailList.length > 1 && index !== payDetailList.length - 1) {
                const totalAmount = Number(form.value.amount);

                // 计算除最后一个支付方式外的所有支付方式金额总和
                let otherAmount = 0;
                for (let i = 0; i < payDetailList.length - 1; i++) {
                  otherAmount += Number(payDetailList[i].amount || 0);
                }

                // 计算最后一个支付方式应有的金额
                const lastAmount = totalAmount - otherAmount;

                if (lastAmount >= 0) {
                  payDetailList[payDetailList.length - 1].amount = lastAmount;
                } else {
                  // 如果计算出的金额小于0，提示用户
                  proxy.$Message.warning("支付方式金额总和已超过付款金额，请调整");
                  // 恥持原值
                  setTimeout(() => {
                    const totalPaymentAmount = Number(payTypeTotalAmount.value);
                    const diff = totalPaymentAmount - totalAmount;
                    row.amount = Number(value) - diff;
                  }, 0);
                }
              }
            },
          },{
            minWidth: 100,
            label: '备注',
            prop: "remarks",
            maxlength: 100,
          }],
        }
      },
    ]
  });



  // 计算支付方式总金额
  const payTypeTotalAmount = computed(() => {
    let totalAmount = 0;
    if (form.value && form.value.payDetailList && form.value.payDetailList.length > 0) {
      form.value.payDetailList.forEach(item => {
        totalAmount = Number(totalAmount) + Number(item.amount || 0);
      });
    }
    return totalAmount;
  });



  // 供应商数据缓存
  const supplierCache = ref({
    data: [],
    timestamp: 0,
    expirationTime: 5 * 60 * 1000 // 缓存过期时间：5分钟
  });

  // 获取供应商列表（带缓存）
  const getSupplierList = async (keyword = '') => {
    try {
      // 检查缓存是否过期
      const now = Date.now();
      if (supplierCache.value.data.length === 0 ||
          now - supplierCache.value.timestamp > supplierCache.value.expirationTime) {
        // 缓存过期或为空，重新获取数据
        const res = await getSupplierSelect();
        if (res && res.data) {
          supplierCache.value.data = res.data;
          supplierCache.value.timestamp = now;
        }
      }

      // 根据关键字过滤数据
      let filteredData = supplierCache.value.data;
      if (keyword) {
        const searchKeyword = keyword.toLowerCase();
        filteredData = supplierCache.value.data.filter(item =>
          (item.name || '').toLowerCase().includes(searchKeyword) ||
          (item.shortName || '').toLowerCase().includes(searchKeyword) ||
          (item.bankName || '').toLowerCase().includes(searchKeyword) ||
          (item.bankAccount || '').toLowerCase().includes(searchKeyword)
        );
      }

      // 更新下拉框数据
      const payeeOpt = proxy.findObject(payinfoOption.value.column, "payee");
      if (payeeOpt) {
        payeeOpt.dicData = filteredData;
      }

      return filteredData;
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      proxy.$message.error('获取供应商列表失败');
      return [];
    }
  };

  // 供应商选择弹窗状态
  const selectSupplierBox = ref({
    open: false,
    loading: false,
    onSelected: null
  });

  // 处理供应商选择
  const handleSupplierSelect = async (supplier) => {
    if (!supplier) {
      proxy.$message.warning('未选择供应商');
      return;
    }

    try {
      selectSupplierBox.value.loading = true;

      // 更新表单数据
      form.value.payee = supplier.name;
      form.value.payeeSimple = supplier.shortName || '';
      form.value.bankAddress = supplier.bankName || supplier.bankAddress || '';
      form.value.bankAccount = supplier.bankAccount || '';

      // 更新请款单收款方信息
      if (paymentRealList.value && paymentRealList.value.length > 0) {
        const hasUnsavedChanges = paymentRealList.value.some(item => !item._saved);
        const confirmMessage = hasUnsavedChanges
          ? `有未保存的请款单数据，更新收款方可能会导致这些更改丢失。是否将${paymentRealList.value.length}条请款单的收款方都更新为"${supplier.name}"？`
          : `是否将${paymentRealList.value.length}条请款单的收款方都更新为"${supplier.name}"？`;

        try {
          const updatePayee = await proxy.$confirm(
            confirmMessage,
            '更新收款方',
            {
              confirmButtonText: '确定更新',
              cancelButtonText: '保持原值',
              type: 'warning',
              distinguishCancelAndClose: true
            }
          );

          if (updatePayee === 'confirm') {
            const oldPaymentRealList = JSON.parse(JSON.stringify(paymentRealList.value));
            paymentRealList.value = paymentRealList.value.map(item => ({
              ...item,
              partyB: supplier.name,
              _needUpdate: item._saved // 标记已保存的数据需要更新
            }));

            // 提供撤销选项，并在点击时显示更详细的信息
            proxy.$notify({
              title: '更新成功',
              message: `已更新${paymentRealList.value.length}条请款单的收款方信息为"${supplier.name}"`,
              type: 'success',
              duration: 8000,
              showClose: true,
              onClick: () => {
                const needUpdateCount = paymentRealList.value.filter(item => item._needUpdate).length;
                proxy.$confirm(
                  `是否要撤销更新操作？\n\n` +
                  `- 总共更新了${paymentRealList.value.length}条请款单\n` +
                  `- 其中${needUpdateCount}条需要保存到数据库\n` +
                  `- 撤销后将恢复到更新前的状态`,
                  '撤销更新',
                  {
                    confirmButtonText: '确认撤销',
                    cancelButtonText: '保持更新',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                  }
                ).then(() => {
                  paymentRealList.value = oldPaymentRealList;
                  proxy.$message.success('已撤销更新操作');
                }).catch(() => {});
              }
            });
          }
        } catch (error) {
          // 用户取消操作，不做任何处理
          console.log('用户取消更新请款单收款方信息');
        }
      }

      // 关闭供应商选择弹窗
      selectSupplierBox.value.open = false;

      // 确保供应商数据被缓存并更新下拉框
      await getSupplierList();

      // 提示用户已完成选择
      proxy.$notify({
        title: '选择供应商成功',
        message: `已选择供应商：${supplier.name}`,
        type: 'success',
        duration: 3000
      });

    } catch (error) {
      console.error('处理供应商选择失败:', error);
      proxy.$message.error({
        message: '处理供应商选择时发生错误，请重试',
        duration: 5000,
        showClose: true
      });
    } finally {
      selectSupplierBox.value.loading = false;
    }
  };

  // 表单验证
  async function validForm() {
    let resultdata = Object.assign({}, form.value);
    // form本身校验
    let formValidRes = await new Promise((resolve) =>{
        proxy.$refs.payInfoForm.validate((valid, done)=>{
            let resultdata = Object.assign({}, form.value);
            resolve({pass: valid, msg: "请完成校验项", data: resultdata});
            // 滚动到第一个错误项
            const isError = document.getElementsByClassName('is-error');
            if (isError[0]) {
              isError[0].scrollIntoView({behavior: 'smooth', block: 'center'});
            }
            done();
        })
    });

    // 验证支付方式
    if (formValidRes.pass) {
      // 检查是否添加了支付方式
      if (!form.value.payDetailList || form.value.payDetailList.length === 0) {
        return {pass: false, msg: "请添加至少一种支付方式", data: formValidRes.data};
      }

      // 检查支付方式总金额是否等于付款金额
      const totalAmount = Number(payTypeTotalAmount.value);
      const formAmount = Number(form.value.amount || 0);
      if (Math.abs(totalAmount - formAmount) > 0.01) {
        return {pass: false, msg: "支付方式总金额必须等于付款金额", data: formValidRes.data};
      }

      // 检查支付方式字段是否完整
      for (let i = 0; i < form.value.payDetailList.length; i++) {
        const item = form.value.payDetailList[i];
        if (!item.type) {
          return {pass: false, msg: `第${i+1}个支付方式未选择支付方式类型`, data: formValidRes.data};
        }
        if (!item.amount || Number(item.amount) <= 0) {
          return {pass: false, msg: `第${i+1}个支付方式金额必须大于0`, data: formValidRes.data};
        }
      }
    }

    return formValidRes;
  }

  // 提交
  async function handleSubmit(){
    let formValidRes = await validForm();

    if(formValidRes.pass == false){
        proxy.$Message.warning(formValidRes.msg);
        return false;
    }

    // 验证请款单数据
    if (!paymentRealList.value || paymentRealList.value.length === 0) {
        proxy.$Message.warning("请先上传Excel导入请款单数据");
        return false;
    }

    let row = Object.assign({}, form.value);

    // 添加的时候，props.partyA才有值
    if (props.partyA) {
      row.partyA = props.partyA;
    }

    // 确保支付方式数据正确
    if (!row.payDetailList) {
      row.payDetailList = [];
    }

    // 处理支付方式数据，确保字段名称正确
    row.payDetailList = row.payDetailList.map(item => {
      return {
        type: item.type,
        amount: item.amount,
        remarks: item.remarks || ''
      };
    });

    // 确保请款单数据正确
    if (!row.paymentRealList) {
      row.paymentRealList = [];
    }

    // 处理请款单数据，确保包含所有必要的字段
    row.paymentRealList = paymentRealList.value.map(item => {
      return {
        partyA: item.partyA || row.partyA,
        partyB: item.partyB || row.payee,
        contractCode: item.contractCode,
        eqAmount: item.eqAmount,
        eqPaymentAmount: item.eqPaymentAmount,
        remarks: item.remarks || '',
        contractFiles: item.contractFiles || '',
        attachmentFiles: item.attachmentFiles || '',
        payInfoId: '' // 将在后端关联付款单ID
      };
    });

    submitLoading.value = true;
    // 处理数组字段转换为字符串
    const submitData = JSON.parse(JSON.stringify(row));
    if (submitData.paymentRealList) {
      submitData.paymentRealList.forEach(item => {
        // contractFiles 和 attachmentFiles 已经是 mach-upload 组件返回的字符串格式
        // 不需要额外处理，直接保存即可
        console.log("提交前的 contractFiles:", item.contractFiles);
        console.log("提交前的 attachmentFiles:", item.attachmentFiles);
      });
    }
    if (Array.isArray(submitData.attachments)) {
      submitData.attachments = submitData.attachments.join(",");
    }
    submit(submitData).then(resp => {
      submitLoading.value = false;
      proxy.$Message({message: resp.data.msg, type: "success"});
      handleCancel(true);
    }).catch(error => {
      console.error("提交失败", error);
      submitLoading.value = false;
      proxy.$Message({message: "提交失败，请联系管理员", type: "error"});
    })
    .catch(() => {
      submitLoading.value = false;
      proxy.$Message({message: "提交异常，请联系管理员", type: "error"});
    });
  }

  // 关闭
  function handleCancel(flag) {
    proxy.$emit('handleCancel', flag);
  }



  // 生成编号（前端生成，不再调用后端）
  function generateCodeLocal() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    // 格式：FKSP-年月日-时分秒-随机数
    return `FKSP-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
  }

  async function init(){
    // 获取是/否字典
    await getDictionaryBiz({code: "yes_no"}).then((resp) => {
        yesNoDic.value = resp.data.data;

        // 监听发票状态变化，控制发票备注显示
        let invoiceStatusOpt = proxy.findObject(payinfoOption.value.column, "invoiceStatus");
        let invoiceRemarkOpt = proxy.findObject(payinfoOption.value.column, "invoiceRemark");

        if (invoiceStatusOpt && invoiceRemarkOpt) {
          // 监听发票状态变化
          watch(() => form.value.invoiceStatus, (newVal) => {
            // 如果选择了'是'，显示发票备注字段
            invoiceRemarkOpt.display = newVal === '1';
          }, { immediate: true });
        }

        // 监听入库状态变化，控制入库备注显示
        let instoreStatusOpt = proxy.findObject(payinfoOption.value.column, "instoreStatus");
        let instoreRemarkOpt = proxy.findObject(payinfoOption.value.column, "instoreRemark");

        if (instoreStatusOpt && instoreRemarkOpt) {
          proxy.$watch(() => form.value.instoreStatus, (val) => {
            instoreRemarkOpt.display = val === "1";
          });
        }
    });

    // 获取供应商下拉数据
    getSupplierSelect().then(res => {
      console.log("供应商下拉数据:", res.data);
      if (res.data.success && res.data.data) {
        // 获取收款单位字段配置
        let payeeOpt = proxy.findObject(payinfoOption.value.column, "payee");
        if (payeeOpt) {
          // 处理不同的数据结构
          let supplierData = [];
          if (Array.isArray(res.data.data)) {
            supplierData = res.data.data;
          } else if (res.data.data && res.data.data.records) {
            supplierData = res.data.data.records;
          }

          // 确保供应商数据包含所有必要的字段
          supplierData = supplierData.map(supplier => {
            return {
              id: supplier.id,
              name: supplier.name,
              shortName: supplier.shortName || supplier.simpleName || '',
              bankName: supplier.bankName || supplier.bankAddress || '',
              bankAccount: supplier.bankAccount || '',
              value: supplier.name, // 添加value字段，确保下拉框可以正确显示和选择
              label: supplier.name  // 添加label字段，确保下拉框可以正确显示和选择
            };
          });

          payeeOpt.dicData = supplierData;
          console.log("设置供应商下拉数据:", supplierData);

          // 全局存储供应商数据，以便在其他地方使用
          window.supplierData = supplierData;
        }
      } else {
        console.error("获取供应商下拉数据失败");
      }
    }).catch(error => {
      console.error("获取供应商下拉数据异常:", error);
    });

    // 修改
    if (props.flowApvContractPayInfo && props.flowApvContractPayInfo.id) {
      showBtns.value = false;
      form.value = props.flowApvContractPayInfo;

      // 确保支付方式列表存在
      if (!form.value.payDetailList) {
        form.value.payDetailList = [];
      }
    }
    // 添加
    else {
      // 使用本地生成编号函数
      form.value.code = generateCodeLocal();

      // 初始化表单
      form.value.partyA = props.partyA;
      form.value.level = "1"; // 默认一般紧急程度
      form.value.bankChange = "0"; // 默认银行信息不变更
      form.value.invoiceStatus = "0"; // 默认不开具发票
      form.value.instoreStatus = "0"; // 默认货物未入库

      // 初始化支付方式列表
      form.value.payDetailList = [];

      // 不自动添加支付方式，等用户填写付款金额后再添加
    }
  }

  // 初始化
  onMounted(() => {
    init();
  });
</script>