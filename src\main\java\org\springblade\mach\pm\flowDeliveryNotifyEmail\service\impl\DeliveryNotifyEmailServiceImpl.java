/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.pm.flowDeliveryNotifyEmail.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.JakartaMailUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.spring.SpringUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.config.SysEmailProperties;
import org.springblade.common.constant.MachConst;
import org.springblade.common.utils.FileUtils;
import org.springblade.common.utils.FreemarkerUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.log.logger.BladeLogger;
import org.springblade.core.tool.utils.Exceptions;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ResourceUtil;
import org.springblade.mach.basis.flowTemplateConf.pojo.entity.FlowTemplateConfEntity;
import org.springblade.mach.basis.flowTemplateConf.service.IFlowTemplateConfService;
import org.springblade.mach.pm.flowDeliveryNotifyEmail.pojo.entity.DeliveryNotifyEmailEntity;
import org.springblade.mach.pm.flowDeliveryNotifyEmail.pojo.vo.DeliveryNotifyEmailVO;
import org.springblade.mach.pm.flowDeliveryNotifyEmail.excel.DeliveryNotifyEmailExcel;
import org.springblade.mach.pm.flowDeliveryNotifyEmail.mapper.DeliveryNotifyEmailMapper;
import org.springblade.mach.pm.flowDeliveryNotifyEmail.service.IDeliveryNotifyEmailService;
import org.springblade.mach.pm.projectSchedule.pojo.entity.FlowProjectScheduleEntity;
import org.springblade.mach.pm.projectSchedule.service.IFlowProjectScheduleService;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.io.*;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货邮箱通知 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class DeliveryNotifyEmailServiceImpl extends BaseServiceImpl<DeliveryNotifyEmailMapper, DeliveryNotifyEmailEntity> implements IDeliveryNotifyEmailService {

	@Autowired
	private SysEmailProperties sysEmailProperties;

	@Autowired
	private IFlowTemplateConfService flowTemplateConfService;

	@Autowired
	private IFlowProjectScheduleService flowProjectScheduleService;

	@Autowired
	private BladeLogger bladeLogger;
	@Override
	public IPage<DeliveryNotifyEmailVO> selectDeliveryNotifyEmailPage(IPage<DeliveryNotifyEmailVO> page, DeliveryNotifyEmailVO deliveryNotifyEmail) {
		return page.setRecords(baseMapper.selectDeliveryNotifyEmailPage(page, deliveryNotifyEmail));
	}


	@Override
	public List<DeliveryNotifyEmailExcel> exportDeliveryNotifyEmail(Wrapper<DeliveryNotifyEmailEntity> queryWrapper) {
		List<DeliveryNotifyEmailExcel> deliveryNotifyEmailList = baseMapper.exportDeliveryNotifyEmail(queryWrapper);
		//deliveryNotifyEmailList.forEach(deliveryNotifyEmail -> {
		//	deliveryNotifyEmail.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeliveryNotifyEmail.getType()));
		//});
		return deliveryNotifyEmailList;
	}

	@Override
	public void toDeliveryNotifyEmail() {
		// 查出大于10分钟以内的数据未发送的
		List<DeliveryNotifyEmailEntity> list = this.lambdaQuery()
				.eq(DeliveryNotifyEmailEntity::getStatus, 0)
				.lt(DeliveryNotifyEmailEntity::getCreateTime, LocalDateTime.now().minusMinutes(10))
				.list();
		if(CollectionUtil.isEmpty(list)){
			return;
		}
		// 按照工单号分组
		Map<String, List<DeliveryNotifyEmailEntity>> groupedByTicketNo = groupByTicketNo(list);

		// 处理分组后的数据
		groupedByTicketNo.forEach((ticketNo, entities) -> {
			//发送发货通知邮件
			try {
				sendEmail(entities);
			} catch (Exception e) {
				throw new RuntimeException("发送邮件失败："+Exceptions.getStackTraceAsString(e));
			}
			try {
				// 更新状态为已发送
				this.lambdaUpdate()
						.set(DeliveryNotifyEmailEntity::getStatus, 1)
						.in(DeliveryNotifyEmailEntity::getId, entities.stream().map(DeliveryNotifyEmailEntity::getId).collect(Collectors.toList()))
						.update();
			} catch (Exception e) {
				bladeLogger.error("updateDeliveryNotifyEmailStatus", "更新发货通知邮件状态出现异常："+ Exceptions.getStackTraceAsString(e));
			}
        });
	}


    private void sendEmail(List<DeliveryNotifyEmailEntity> entities) throws Exception {
		Map<String,Object> emailData = Maps.newHashMap();
		String poiTl = "classpath:/poi-tl/delivery_notify_domestic.docx";//国内发货通知
		String tradeType = entities.get(0).getTradeType();
		if(StrUtil.equals(tradeType,"1")){
			poiTl = "classpath:/poi-tl/delivery_notify_abroad.docx";//国外发货通知
		}
		Resource resource = ResourceUtil.getResource(poiTl);
		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		Configure configure = Configure.builder()
				.bind("list",policy)
				.useSpringEL()
				.build();
		// 项目销售
		FlowProjectScheduleEntity one = flowProjectScheduleService.lambdaQuery()
			.eq(FlowProjectScheduleEntity::getWorkNum, entities.get(0).getTicketNo()).one();
		User saleUser = UserCache.getUser(one.getSaleId());
		String saleUserName = StrUtil.sub(saleUser.getRealName(),0,1)+"总";
		// 内容邮件
		String toEmail = "<EMAIL>";
		if(StrUtil.isNotBlank(saleUser.getEmail()) ){
			if(saleUser.getRealName().equals("周丽娟")){
				toEmail = saleUser.getEmail();
			}
		}
		String notifyTime = DateUtil.format(entities.get(0).getNotifyTime(), "yyyy-MM-dd");
		String arrivalDate = DateUtil.format(entities.get(0).getArrivalDate(), "yyyy-MM-dd");
		String deliveryTime =DateUtil.format(entities.get(0).getDeliveryTime(),"yyyy-MM-dd");
		emailData.put("notifyTime",notifyTime);
		emailData.put("toEmail",toEmail);
		emailData.put("arrivalDate",StringUtils.isBlank(arrivalDate)?"":"到账日期："+arrivalDate);
		emailData.put("deliveryTime",deliveryTime);
		emailData.put("customerName",entities.get(0).getCustomerName());
		emailData.put("saleUserName",saleUserName);
		emailData.put("ticketNo",entities.get(0).getTicketNo());
		emailData.put("remarks",entities.get(0).getRemarks());
		emailData.put("deliveryNotifyNo",entities.get(0).getDeliveryNotifyNo());
		emailData.put("list",entities);
		entities.forEach(entity -> {
			//factory是字符串
			String factoryName = entity.getFactoryName();
			StringBuilder sb = new StringBuilder();
			Func.toStrList(factoryName).forEach(factory -> {
				String name = DictBizCache.getValue("factory_name", factory);
				sb.append(name).append("|");
			});
			entity.setFactoryName(sb.toString());
		});
		// 通过 XWPFTemplate 编译文件并渲染数据到模板中
		XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(),configure).render(emailData);
		// 生成到word字符流
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		template.writeAndClose(fos);
		ByteArrayInputStream inputStream = new ByteArrayInputStream(fos.toByteArray());
		// 邮件标题
		String fileName = StrUtil.format("{}{}{}发货通知单{}",
				entities.get(0).getDeliveryNotifyNo(),
				entities.get(0).getCustomerName(),
				entities.get(0).getTicketNo(),
				DateUtil.formatDate(entities.get(0).getNotifyTime()));
		File tempFile = createTempFile(fileName, inputStream);
		// 发送邮件
		String account = sysEmailProperties.getAccount().get(0);
		// 邮件内容
		String content = getContent(fileName,saleUserName,tradeType);
		String to = "<EMAIL>";
		String saleUserEmail = saleUser.getEmail();
		if (StringUtils.isBlank(saleUserEmail)) {
			saleUserEmail = saleUser.getAccount() + "@machtech.com.cn";
		}
		if("sunchaobo".equals(saleUser.getAccount())){
			saleUserEmail = "";
		}
		if (StringUtils.isNotBlank(saleUserEmail)) {
			to= to+","+saleUserEmail;
		}
		String cc = "<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>," +
				"<EMAIL>,"+
				"<EMAIL>";
		if("dev".equals(SpringUtil.getActiveProfile())){
			to = "<EMAIL>";
			cc = "";
		}
		String bcc = "";
		MailAccount mailAccount = getMailAccount(account);
		JakartaMailUtil.send(mailAccount,
				StrUtil.splitTrim(to, CharUtil.COMMA),
				StrUtil.splitTrim(cc, CharUtil.COMMA),
				StrUtil.splitTrim(bcc, CharUtil.COMMA), fileName, content, true, tempFile);
		FileUtils.delete(tempFile);//发完删除临时文件

	}

	@NotNull
	private MailAccount getMailAccount(String account) {
		MailAccount mailAccount = new MailAccount();
		mailAccount.setHost("smtphz.qiye.163.com");
		mailAccount.setPort(465);//默认端口25 , ssl 465
		mailAccount.setSslEnable(true);
		mailAccount.setSslProtocols("TLSv1.2");
		mailAccount.setFrom(account);
		mailAccount.setUser(account);
		mailAccount.setPass(sysEmailProperties.getPassword().get(0));
		mailAccount.setAuth(true);
		mailAccount.setDebug(false);
		return mailAccount;
	}

	private String getContent(String fileName,String saleUserName, String tradeType) {
		// 正文 , 获取延期通知email模版
		FlowTemplateConfEntity emailTpl;
		if(StrUtil.equals(tradeType,"0")){
			emailTpl = flowTemplateConfService.getOneTemplateConf(FlowTemplateConfEntity.delivery_notify_domestic_email, "0");
		}else{
			emailTpl = flowTemplateConfService.getOneTemplateConf(FlowTemplateConfEntity.delivery_notify_abroad_email, "0");
		}
		if (null == emailTpl) {
			bladeLogger.error("发货通知邮件底部内容","不应该取不到Template配置，这里请注意。");
			throw new RuntimeException("发货通知邮件底部内容不应该取不到Template配置，这里请注意。");
		}
		// 生成邮件内容
		Map<String,Object> data = new HashMap<>();
		String emailTplName = emailTpl.getCode() + "_" + emailTpl.getCompany();
		data.put("fileName",fileName);
		data.put("saleUserName",saleUserName);
        return FreemarkerUtil.getInstance().mergeTemplateIntoString(emailTplName, emailTpl.getContent(), data);
	}

	private Map<String, List<DeliveryNotifyEmailEntity>> groupByTicketNo(List<DeliveryNotifyEmailEntity> list) {
		return list.stream()
				.filter(entity -> entity.getDeliveryNotifyNo() != null)
				.collect(Collectors.groupingBy(DeliveryNotifyEmailEntity::getDeliveryNotifyNo));
	}

	private File createTempFile(String fileName, ByteArrayInputStream fos) {
		try {
			// 清理文件名中的非法字符
			String cleanFileName = fileName.replaceAll("[<>:\"/\\\\|?*]", "_");
			File tempFile = File.createTempFile(cleanFileName, ".docx");
			FileUtils.copyInputStreamToFile(fos,tempFile);
			return tempFile;
		} catch (IOException e) {
			throw new RuntimeException("Failed to create temporary file", e);
		}
	}


    @Override
    public String getDeliveryNotifyNo(String code) {
		List<DeliveryNotifyEmailEntity> list = this.lambdaQuery().list();
		if (list == null || list.isEmpty()) {
			return createDeliveryNotifyNo();
		}

		// 根据code查询最新的一条记录
		DeliveryNotifyEmailEntity one = this.lambdaQuery()
			.eq(DeliveryNotifyEmailEntity::getTicketNo, code)
			.eq(DeliveryNotifyEmailEntity::getStatus, MachConst.NO.getCode())
			.orderByDesc(DeliveryNotifyEmailEntity::getCreateTime)
			.last("limit 1")
			.one();

		if (one != null) {
			return handleExistingRecord(one);
		} else {
			DeliveryNotifyEmailEntity last = this.lambdaQuery()
				.orderByDesc(DeliveryNotifyEmailEntity::getCreateTime)
				.last("limit 1")
				.one();
			return generateNewDeliveryNotifyNo(last);
		}
	}

	private String handleExistingRecord(DeliveryNotifyEmailEntity one) {
		String deliveryNotifyNo = one.getDeliveryNotifyNo();
		Date createTime = one.getCreateTime();
		Date now = new Date();
		long diff = cn.hutool.core.date.DateUtil.between(createTime, now, DateUnit.MINUTE);
		if (diff < 10) {
			return deliveryNotifyNo;
		} else {
			return generateNewDeliveryNotifyNo(one);
		}
	}

	private String generateNewDeliveryNotifyNo(DeliveryNotifyEmailEntity last) {
		String yearSuffix = org.springblade.core.tool.utils.DateUtil.format(new Date(), "yy");
		int currentSequence = getCurrentSequence(last != null ? last.getDeliveryNotifyNo() : null);
		int newSequence = currentSequence + 1;

		// 确保序列号不会溢出
		synchronized (this) {
			return "FT" + yearSuffix + String.format("%03d", newSequence);
		}
	}

	private int getCurrentSequence(String code) {
		if (code == null || code.length() < 3) {
			return 0; // 或者其他默认值
		}
		try {
			return Integer.parseInt(code.substring(code.length() - 3));
		} catch (NumberFormatException | StringIndexOutOfBoundsException e) {
			return 0; // 或者其他默认值
		}
	}

	private String createDeliveryNotifyNo() {
		return "FT" + org.springblade.core.tool.utils.DateUtil.format(new Date(), "yy") + String.format("%03d", 1);
	}



}
