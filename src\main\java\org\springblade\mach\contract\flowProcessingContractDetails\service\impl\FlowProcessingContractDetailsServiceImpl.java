/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowProcessingContractDetails.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.aspose.cells.Worksheet;
import com.aspose.words.LoadFormat;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Maps;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DeptCache;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.MachConst;
import org.springblade.common.utils.*;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.logger.BladeLogger;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.mach.basis.customer.pojo.entity.FlowCustomerEntity;
import org.springblade.mach.contract.flowElectricalCabinetContractDetails.pojo.entity.FlowElectricalCabinetContractDetailsEntity;
import org.springblade.mach.contract.flowOutContractPayInfo.pojo.entity.FlowOutContractPayInfoEntity;
import org.springblade.mach.contract.flowOutContractPayInfo.service.IFlowOutContractPayInfoService;
import org.springblade.mach.contract.flowProcessingContract.pojo.entity.FlowProcessingContractEntity;
import org.springblade.mach.contract.flowProcessingContract.service.IFlowProcessingContractService;
import org.springblade.mach.contract.flowProcessingContractDetails.excel.FlowProcessingContractDetailsExcel;
import org.springblade.mach.contract.flowProcessingContractDetails.mapper.FlowProcessingContractDetailsMapper;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.dto.FlowProcessingContractDetailsDTO;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.entity.FlowProcessingContractDetailsEntity;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.vo.FlowProcessingContractDetailsVO;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.vo.FlowProcessingInfoVo;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.vo.FlowProcessingItemVo;
import org.springblade.mach.contract.flowProcessingContractDetails.service.IFlowProcessingContractDetailsService;
import org.springblade.mach.contract.flowProcessingContractItem.pojo.entity.FlowProcessingContractItemEntity;
import org.springblade.mach.contract.flowProcessingContractItem.pojo.vo.FlowProcessingContractItemVO;
import org.springblade.mach.contract.flowProcessingContractItem.service.IFlowProcessingContractItemService;
import org.springblade.mach.contract.flowProcessingContractItem.wrapper.FlowProcessingContractItemWrapper;
import org.springblade.mach.contract.flowProcessingContractPayment.pojo.entity.FlowProcessingContractPaymentEntity;
import org.springblade.mach.contract.flowProcessingContractPayment.pojo.vo.FlowProcessingContractPaymentVO;
import org.springblade.mach.contract.flowProcessingContractPayment.service.IFlowProcessingContractPaymentService;
import org.springblade.mach.contract.flowProcessingContractPayment.wrapper.FlowProcessingContractPaymentWrapper;
import org.springblade.mach.contract.flowProcessingContractQc.pojo.entity.ProcessingContractQcEntity;
import org.springblade.mach.contract.flowProcessingContractQc.service.IFlowProcessingContractQcService;
import org.springblade.mach.pm.flowProjectEqProgress.pojo.entity.FlowProjectEqProgressEntity;
import org.springblade.mach.pm.flowProjectEqProgress.service.IFlowProjectEqProgressService;
import org.springblade.mach.pm.flowProjectPay.pojo.entity.FlowProjectPayEntity;
import org.springblade.mach.pm.flowProjectPay.service.IFlowProjectPayService;
import org.springblade.mach.pm.projectDetails.service.IFlowProjectDetailsService;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springblade.mach.pm.projectEqDetails.service.IFlowProjectEqDetailsService;
import org.springblade.mach.pm.projectSchedule.pojo.entity.FlowProjectScheduleEntity;
import org.springblade.mach.pm.projectSchedule.service.IFlowProjectScheduleService;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springblade.modules.system.pojo.entity.Dept;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 加工合同 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
@Transactional(readOnly = true)
public class FlowProcessingContractDetailsServiceImpl extends BaseServiceImpl<FlowProcessingContractDetailsMapper, FlowProcessingContractDetailsEntity> implements IFlowProcessingContractDetailsService {

	@Autowired
	private BladeLogger bladeLogger;
	@Autowired
	private IFlowProcessingContractPaymentService flowProcessingContractPaymentService;
	@Lazy
	@Autowired
	private IFlowProcessingContractService flowProcessingContractService;
	@Lazy
	@Autowired
	private IFlowProcessingContractDetailsService flowProcessingContractDetailsService;
	@Lazy
	@Autowired
	private IFlowProcessingContractItemService flowProcessingContractItemService;
	@Autowired
	private IFlowService flowService;
	@Lazy
	@Autowired
	private IFlowProjectEqDetailsService flowProjectEqDetailsService;
	@Lazy
	@Autowired
	private IFlowProjectScheduleService flowProjectScheduleService;
	@Autowired
	private IFlowProjectEqProgressService flowProjectEqProgressService;
	@Autowired
	private OssBuilder ossBuilder;
	@Lazy
	@Autowired
	private IFlowProcessingContractQcService flowProcessingContractQcService;
	@Lazy
	@Autowired
	private IFlowOutContractPayInfoService flowOutContractPayInfoService;
	@Autowired
	private IUserService userService;

	@Override
	public FlowProcessingContractDetailsVO findById(Long id) {
		return baseMapper.findById(id);
	}

	@Override
	public IPage<FlowProcessingContractDetailsVO> selectFlowProcessingContractDetailsPage(IPage<FlowProcessingContractDetailsVO> page, FlowProcessingContractDetailsVO flowProcessingContractDetails) {
		List<FlowProcessingContractDetailsVO> list = baseMapper.selectFlowProcessingContractDetailsPage(page, flowProcessingContractDetails);
		return page.setRecords(list);
	}


	@Override
	public List<FlowProcessingContractDetailsExcel> exportFlowProcessingContractDetails(Wrapper<FlowProcessingContractDetailsEntity> queryWrapper) {
		List<FlowProcessingContractDetailsExcel> flowProcessingContractDetailsList = baseMapper.exportFlowProcessingContractDetails(queryWrapper);
		//flowProcessingContractDetailsList.forEach(flowProcessingContractDetails -> {
		//	flowProcessingContractDetails.setTypeName(DictCache.getValue(DictEnum.YES_NO, FlowProcessingContractDetails.getType()));
		//});
		return flowProcessingContractDetailsList;
	}

	/**
	 * 获取当年当月最大的合同编号
	 * @param flowProcessingContract
	 * @return
	 */
    @Override
    public Integer getMaxCode(FlowProcessingContractEntity flowProcessingContract) {
        return baseMapper.getMaxCode(flowProcessingContract);
    }

	/**
	 * 获取流程处理表单
	 * {@link org.springblade.flow.business.service.impl.FlowServiceImpl#getAuditFormVue 动态调用本方法}
	 * @param flow
	 * @return
	 */
	public String auditForm(BladeFlow flow){
		String view = "flowContract/flowProcessingContractDetails/flowProcessingContractDetailsAudit";

		if(StringUtil.isBlank(flow.getBusinessId())){
			return view;
		}

		// 环节编号
		String taskDefKey = flow.getTaskDefinitionKey();
		if(StringUtil.isBlank(taskDefKey)){
			return view;
		}

		// 已经过了自己参与的环节
		if (flow.isFinishTask()) {
			//view = "processing/flowProcessingContractDetailsAudit";
			return view;
		}

//		switch (taskDefKey){
//			case "sign_contract"://提交签订合同
//			case "reEditContract"://重新修改合同
//			case "leaderAudit"://部门领导审批
//			case "dgmAudit"://天津工厂常务副总审核
//			case "callback_contract"://上传双章合同
//				break;
//		}

		return view;
	}

	/**
	 * 查看合同获取数据
	 * @param id
	 * @return
	 */
	@Override
	public R getViewReportData(Long id) {
		FlowProcessingContractDetailsEntity detail = this.getById(id);
		FlowProcessingContractEntity flowProcessingContract = flowProcessingContractService.getById(detail.getContractId());
		FlowProcessingContractItemEntity flowProcessingContractItem = flowProcessingContractItemService.getById(detail.getContractItemId());

//		// 付款申请单列表（请款单类型）
//		List<FlowProcessingContractPaymentEntity> flowProcessingContractPaymentList = flowProcessingContractPaymentService.lambdaQuery()
//			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[2])
//			.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, id)
//			.list();
		// 付款申请单列表（合同类型）
		List<FlowProcessingContractPaymentEntity> flowProcessingContractPaymentList = flowProcessingContractPaymentService.lambdaQuery()
			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])//查合同类型的
			.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, id)
			.eq(FlowProcessingContractPaymentEntity::getPaymentStatus,MachConst.YES.getCode())//已经生成付款单的
			.list();

		Kv data = Kv.init()
			.set("detail", detail)//合同
			.set("flowProcessingContract",flowProcessingContract)//可做合同产品
			.set("flowProcessingContractItem",flowProcessingContractItem)//可做合同产品
			.set("flowProcessingContractPaymentList", flowProcessingContractPaymentList);
		return R.data(data);
	}

	/**
	 * 查看合同，输出PDF
	 * @param id	合同id
	 * @param request
	 * @param response
	 */
	@Override
	@Transactional(readOnly = false)
	public void outputContractPdf(Long id, HttpServletRequest request, HttpServletResponse response) {
		try{
			Kv kv = this.getContractPdf(id);
			if(null == kv){
				return;
			}
			byte[] pdfBytes = (byte[]) kv.get("bytes");
			ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);

			// 生成PDF 输出到页面
			String fileName = kv.getStr("fileName");
			ServletUtil.setDownloadHeader(request,response,fileName);
			ServletUtil.write(response, inputStream, "application/octet-stream");
		}catch (Exception e){
			log.error("查看加工合同异常：",e);
		}
	}
	/**
	 * 获取 PDF文件流 pdf名称
	 */
	@Override
	@Transactional(readOnly = false)
	public Kv getContractPdf(Long id) throws Exception {
		if(null == id || id <= 0){
			return null;
		}
		FlowProcessingContractDetailsVO flowProcessingContractDetails = this.findById(id);

		String contractName = "加工合同【"+flowProcessingContractDetails.getCode()+"】";
		// 渲染文件存在，则直接展示
		if(StrUtil.isNotBlank(flowProcessingContractDetails.getRenderFile())){
			String f = ossBuilder.template().fileLink(flowProcessingContractDetails.getRenderFile());
			InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(f));
			byte[] pdfBytes =  IoUtil.readToByteArray(inputStream);
			return Kv.init().set("bytes",pdfBytes).set("fileName",contractName+".pdf");
		}

		FlowProjectEqDetailsEntity flowProjectEqDetails = flowProjectEqDetailsService.getById(flowProcessingContractDetails.getEqDetailsId());
		FlowProjectScheduleEntity flowProjectSchedule = flowProjectScheduleService.getById(flowProjectEqDetails.getScheduleId());

//		// 获取计划发货日期
//		FlowProjectEqProgressEntity flowProjectEqProgress = flowProjectEqProgressService.getByEqDetailId(flowProjectEqDetails.getId());
//		if(null != flowProjectEqProgress && null != flowProjectEqProgress.getPlanDriverDate()){
//			flowProcessingContractDetails.setDeliveryTime(DateUtil.formatDate(flowProjectEqProgress.getPlanDriverDate()));
//			flowProcessingContractDetails.setDeliveryTime2(DateUtil.format(flowProjectEqProgress.getPlanDriverDate(),"yyyy年MM月dd日"));
//		}

		flowProcessingContractDetails.setEqCode(flowProjectEqDetails.getCode());
		flowProcessingContractDetails.setEqName(flowProjectEqDetails.getEqName());
//		flowProcessingContractDetails.setDeliveryTime(null != flowProjectEqProgress.getPlanDriverDate() ? DateUtil.formatDate(flowProjectEqProgress.getPlanDriverDate()) : null);
//		flowProcessingContractDetails.setDeliveryTime2(null != flowProjectEqProgress.getPlanDriverDate() ? DateUtil.format(flowProjectEqProgress.getPlanDriverDate(),"yyyy年MM月dd日") : null);
		flowProcessingContractDetails.setWorkNum(flowProjectSchedule.getWorkNum());
		flowProcessingContractDetails.setCustomerName(CompanyNameUtil.simpleCompanyName(flowProjectEqDetails.getCustomerName()));
		flowProcessingContractDetails.setToAddress(flowProjectSchedule.getToAddress());

		FlowProcessingContractItemEntity flowProcessingContractItem = flowProcessingContractItemService.getById(flowProcessingContractDetails.getContractItemId());
		FlowProcessingContractItemVO flowProcessingContractItemVO = FlowProcessingContractItemWrapper.build().entityVO(flowProcessingContractItem);
		flowProcessingContractItemVO.setEqName(flowProjectEqDetails.getEqName());
		if(null != flowProcessingContractItem.getItemDeliveryDate()) {
			flowProcessingContractItemVO.setDeliveryTime(DateUtil.formatDate(flowProcessingContractItem.getItemDeliveryDate()));
			flowProcessingContractItemVO.setDeliveryTime2(DateUtil.format(flowProcessingContractItem.getItemDeliveryDate(), "yyyy年MM月dd日"));
		}
		flowProcessingContractItemVO.setWorkNum(flowProjectSchedule.getWorkNum());
		flowProcessingContractItemVO.setCustomerName(CompanyNameUtil.simpleCompanyName(flowProjectEqDetails.getCustomerName()));
		flowProcessingContractItemVO.setUnitPrice(new BigDecimal(flowProcessingContractItemVO.getAmount()));
		flowProcessingContractItemVO.setSort(1);

		// 获取合同数据
		List<FlowProcessingContractItemVO> contractList = new ArrayList<>();
		contractList.add(flowProcessingContractItemVO);

		// 计算总金额
		BigDecimal totalPrice = new BigDecimal("0");
		DecimalFormat decimalFormat = new DecimalFormat("#.00");
		for (FlowProcessingContractItemVO e : contractList) {
			// 记录总金额
			totalPrice = totalPrice.add(new BigDecimal(e.getAmount() + ""));
		}

		contractList.forEach(e->{
			String eqName = e.getContent();
			if (!StrUtil.endWith(eqName,"设备")) {
				eqName += "设备";
			}
			e.setEqName(eqName);
		});
		// 排序
		//contractList = contractList.stream().sorted(Comparator.comparing(FlowProcessingContractDetailsEntity::getSort)).collect(Collectors.toList());

		// 记录签订时间
		flowProcessingContractDetails.setSigningDate(DateUtil.formatDate(flowProcessingContractDetails.getCreateTime()));
		// 记录金额
		flowProcessingContractDetails.setTotalPrice(decimalFormat.format(totalPrice.doubleValue()));
		flowProcessingContractDetails.setTotalAmountInWords(ConvertUpMoney.toChinese(totalPrice));
		// 记录签订人
		User user = UserCache.getUser(flowProcessingContractDetails.getCreateUser());
		flowProcessingContractDetails.setApplicantName(null != user ? user.getName() : "");
		// 甲方生产基地名称
		{
			// 天津
			if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(flowProcessingContractDetails.getPartyA())) {
				flowProcessingContractDetails.setPartyAName(FlowProjectScheduleEntity.COMPANYNAME[2]);
			}
			// 江苏
			else if (FlowProjectEqDetailsEntity.FACTORY_TYPE[1].equals(flowProcessingContractDetails.getPartyA())) {
				flowProcessingContractDetails.setPartyAName(FlowProjectScheduleEntity.COMPANYNAME[3]);
			}
		}

		// 乙方公司名称 这里要取到签订公司(生产基地)的名字
		flowProcessingContractDetails.setPartyBName(FlowProjectEqDetailsEntity.FACTORY_NAME[Integer.valueOf(flowProcessingContractDetails.getPartyB())-1]);

		// 可做合同信息
		FlowProcessingContractEntity contract = flowProcessingContractService.getById(flowProcessingContractDetails.getContractId());
		// 可做合同明细
		FlowProcessingContractItemEntity contractItem = flowProcessingContractItemService.getById(flowProcessingContractDetails.getContractItemId());
//		//颜色
		flowProcessingContractDetails.setEquipmentColor(contractItem.getEquipmentColor());

		//  先生成合同word，再生成PDF
		try {
			// 校验生成合同类型
			// 甲方-合同章
			PictureRenderData partyAGz = null;
			// 乙方-合同章
			PictureRenderData partyBGz = null;
			// 甲方-签字
			PictureRenderData partyAPeopleSign = null;
			// 乙方-签字
			PictureRenderData partyBPeopleSign = null;
			// 空白图
			PictureRenderData emptyPic = Pictures.ofBufferedImage(SignUtil.init().setSize(1, 1).generateSignatureImage(null).getImage(), PictureType.PNG).create();
			// 判断是否压送罐
			String itemTypeStr = FlowProcessingContractItemEntity.TYPES[1].equals(contractItem.getType()) ? "_2" : "";
			// poi-tl模版
			String poiTl = StrUtil.format("classpath:/poi-tl/contract/processing_contract_{}_{}{}.docx",flowProcessingContractDetails.getPartyA(),flowProcessingContractDetails.getPartyB(),itemTypeStr);//加工合同


			// 根据合同签订状态，区分单章、双章、无章
			String status = String.valueOf(flowProcessingContractDetails.getStatus());
			// 无章
			if(FlowProcessingContractDetailsEntity.STATUS[0].equals(status)){
				partyAGz = emptyPic;
				partyBGz = emptyPic;

				partyAPeopleSign = emptyPic;
				partyBPeopleSign = emptyPic;
			}
			// 单章
			else if(FlowProcessingContractDetailsEntity.STATUS[1].equals(status)){
				// 甲方签名
				BufferedImage partyAPeopleSignImg = SignUtil.init().generateSignatureImage(flowProcessingContractDetails.getApplicantName()).getImage();
				partyAPeopleSign = Pictures.ofBufferedImage(partyAPeopleSignImg, PictureType.PNG).create();
				// 甲方合同章
				partyAGz = Pictures.ofStream(ResourceUtil.getResource("classpath:/imgs/contract_seal_"+MachUtils.factoryToCompany(flowProcessingContractDetails.getPartyA())+".png").getInputStream()).create();

				// 乙方签名
				partyBPeopleSign = emptyPic;
				// 乙方合同章
				partyBGz = emptyPic;
			}
			// 双章
			else if(FlowProcessingContractDetailsEntity.STATUS[2].equals(status)){
				// 双章状态合同，直接显示files pdf附件
				// |upload/2024-10-06/7d908b0e81c84c91807209fb0a41b6d3.pdf?fn=武汉华工密炼机上辅机系统及环保设备合同.pdf
				String files = flowProcessingContractDetails.getFiles();
				if(StrUtil.isBlank(files)){
					bladeLogger.error("获取加工合同附件","获取合同附件files为空"+id);
					return null;
				}
				List<String> split = StrUtil.split(files, "|",true,true);
				if(split.isEmpty())
				{
					bladeLogger.error("获取加工合同附件","获取合同附件split为空"+id);
					return null;
				}

				// 上传的双章合同
				files = ossBuilder.template().fileLink(split.get(0));
				InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(files));
				// 若有质检文件，则拼接质检文件
				{
					ProcessingContractQcEntity qc = flowProcessingContractQcService.lambdaQuery().eq(ProcessingContractQcEntity::getContractDetailsId, id)
						.eq(ProcessingContractQcEntity::getStatus, Integer.valueOf(MachConst.YES.getCode()))
						.last("limit 0,1")
						.one();
					if (null != qc) {
						List<String> qcFileSplit = StrUtil.split(qc.getFiles(), "|", true, true);
						if (qcFileSplit.isEmpty()) {
							bladeLogger.error("获取加工合同合格证", "获取合格证失败，id:" + qc.getId());
							return null;
						}
						String qcFiles = ossBuilder.template().fileLink(qcFileSplit.get(0));
						InputStream qcInputStream = URLUtil.getStream(URLUtil.toUrlForHttp(qcFiles));

						// 合并
						InputStream[] isAry = new InputStream[]{inputStream, qcInputStream};
						ByteArrayOutputStream os2 = new ByteArrayOutputStream();
						PdfUtil.getInstance().concatPdf(isAry, os2);
						inputStream = null;
						inputStream = new ByteArrayInputStream(os2.toByteArray());
					}
				}
				byte[] pdfBytes =  IoUtil.readToByteArray(inputStream);
				return Kv.init().set("bytes",pdfBytes).set("fileName",contractName+".pdf");

//				// 生成PDF 输出到页面
//				String fileName = contractName+".pdf";
//				ServletUtil.setDownloadHeader(request,response,fileName);
//				//ServletOutputStream outputStream = response.getOutputStream();
//				ServletUtil.write(response, inputStream);
//				return;

				/*
				// 甲方签名
				BufferedImage partyAPeopleSignImg = SignUtil.init().generateSignatureImage(flowProcessingContractDetails.getApplicantName()).getImage();
				partyAPeopleSign = Pictures.ofBufferedImage(partyAPeopleSignImg, PictureType.PNG).create();
				// 甲方合同章
				partyAGz = Pictures.ofStream(ResourceUtil.getResource("classpath:/imgs/contract_seal_"+MachUtils.factoryToCompany(flowProcessingContractDetails.getPartyA())+".png").getInputStream()).create();


//				// 乙方签名
//				BufferedImage partyBPeopleSignImg = SignUtil.init().generateSignatureImage(flowProcessingContractDetails.getApproverName()).getImage();
//				partyBPeopleSign = Pictures.ofBufferedImage(partyBPeopleSignImg, PictureType.PNG).create();
				// 乙方合同章
				partyBGz = Pictures.ofStream(ResourceUtil.getResource("classpath:/imgs/contract_seal_end.png").getInputStream()).create();
				*/
			}else{
				LogUtils.error(getClass(),"没有此状态");
				return null;
			}

			// 获取 Word 模板所在路径
			Resource resource = ResourceUtil.getResource(poiTl);
			LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
			// 配置
			Configure configure = Configure.builder()
				.bind("cList", policy)
				.useSpringEL()
				.build();

			Map<String,Object> data = new HashMap<>();
			data.put("c", flowProcessingContractDetails);
			data.put("cList", contractList);

			data.put("partyAPeopleSign",partyAPeopleSign);//甲方签名图
			data.put("partyAGz",partyAGz);//甲方合同章

			data.put("partyBPeopleSign",partyBPeopleSign);//乙方签名图
			data.put("partyBGz",partyBGz);//乙方合同章
			// 通过 XWPFTemplate 编译文件并渲染数据到模板中
			XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(),configure).render(data);

			// 生成到word字符流
			ByteArrayOutputStream fos = new ByteArrayOutputStream();
			template.writeAndClose(fos);
			ByteArrayInputStream inputStream = new ByteArrayInputStream(fos.toByteArray());
			fos = null;
//			// 生成PDF 输出到页面
//			String fileName = "加工合同【"+flowProcessingContractDetails.getCode()+"】.pdf";
//			ServletUtil.setDownloadHeader(request,response,fileName);
//			ServletOutputStream outputStream = response.getOutputStream();

			// 单章状态
			if(FlowProcessingContractDetailsEntity.STATUS[1].equals(status)) {
				// 生成pdf
				ByteArrayOutputStream os0 = new ByteArrayOutputStream();
				DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, os0);
				ByteArrayInputStream is0 = new ByteArrayInputStream(os0.toByteArray());

				// 附件excel转pdf
				List<ByteArrayInputStream> is1 = new ArrayList<>();
				List<FlowProcessingInfoVo> datas = this.getContractFilesData(contractItem);
				for (FlowProcessingInfoVo info : datas) {
					// 读取数据按模版生成
					ByteArrayOutputStream os = new ByteArrayOutputStream();
					Kv params = Kv.init();
					params.set("info",info);
					params.set("r",info.getItems());//列表
					XlsUtil.getInstance().mergeTemplate(ResourceUtil.getResource("classpath:/easyexcel/processing_details.xlsx").getInputStream(),os,params);

					// 合并单元格
					ByteArrayOutputStream os2 = new ByteArrayOutputStream();
					List<Kv> list = info.getMerges().stream().map(merge -> Kv.init()
                        .set("firstRowIndex",merge.getFirstRowIndex())
                        .set("firstColumnIndex",merge.getFirstColumnIndex())
                        .set("lastRowIndex",merge.getLastRowIndex())
                        .set("lastColumnIndex",merge.getLastColumnIndex())).collect(Collectors.toList());
					XlsUtil.getInstance().mergeCell(new ByteArrayInputStream(os.toByteArray()),os2,list);
					is1.add(new ByteArrayInputStream(os2.toByteArray()));
				}

				List<ByteArrayOutputStream> osList = new ArrayList<>();
				for (InputStream stream : is1) {
					ByteArrayOutputStream os = new ByteArrayOutputStream();
					// 自动行高
					XlsUtil.Convert2PdfLisener convert2PdfLisener = new XlsUtil.Convert2PdfLisener() {
						@Override
						public void row(Worksheet worksheet, int rowIndex, int maxRowIndex) throws Exception {
							int dataEndRow = maxRowIndex-6;
							if(rowIndex > 3 && rowIndex < dataEndRow){
								worksheet.autoFitRow(rowIndex);
							}
						}
					};
					XlsUtil.getInstance().convert2Pdf(stream, os, convert2PdfLisener);
					osList.add(os);
				}

				// 合并pdf
				ByteArrayOutputStream os2 = new ByteArrayOutputStream();
				InputStream[] isAry = new InputStream[1+osList.size()];
				int i = 0;
				isAry[i++] = is0;
				for (ByteArrayOutputStream os : osList) {
					ByteArrayInputStream is = new ByteArrayInputStream(os.toByteArray());
					isAry[i++] = is;
				}
				PdfUtil.getInstance().concatPdf(isAry,os2);

				//生成骑缝章,输出
				InputStream is2 = new ByteArrayInputStream(os2.toByteArray());
				BufferedImage seal = ImageIO.read(ResourceUtil.getResource("classpath:/imgs/contract_seal_"+MachUtils.factoryToCompany(flowProcessingContractDetails.getPartyA())+".png").getInputStream());
				ByteArrayOutputStream qfzPdfOutputStream = new ByteArrayOutputStream();//pdf
				PdfUtil.getInstance().addQfz(is2,qfzPdfOutputStream,seal,106);
				byte[] pdfBytes = IoUtil.readToByteArray(new ByteArrayInputStream(qfzPdfOutputStream.toByteArray()));
				return Kv.init().set("bytes",pdfBytes).set("fileName",contractName+".pdf");
			}
			// 双章状态，渲染保存起来
			else if(FlowProcessingContractDetailsEntity.STATUS[2].equals(status)){
				ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();//pdf
				DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, pdfOutputStream);
				byte[] pdfBytes = IoUtil.readToByteArray(new ByteArrayInputStream(pdfOutputStream.toByteArray()));

				//保存到数据库
				{
					ByteArrayInputStream pdfInputStream = new ByteArrayInputStream(pdfBytes);
					String fileName = contractName+".pdf";
					BladeFile bladeFile = ossBuilder.template().putFile(fileName, pdfInputStream);
					flowProcessingContractDetails.setRenderFile(bladeFile.getName()+"?fn="+ URLEncodeUtil.encodeAll(fileName));
					// 保存渲染路径
					this.lambdaUpdate()
						.set(FlowProcessingContractDetailsEntity::getRenderFile,flowProcessingContractDetails.getRenderFile())
						.eq(FlowProcessingContractDetailsEntity::getId,flowProcessingContractDetails.getId())
						.update();
				}

				return Kv.init().set("bytes",pdfBytes).set("fileName",contractName+".pdf");
			}
			// 非单章状态
			else{
				ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();//pdf
				DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, pdfOutputStream);
				byte[] pdfBytes = IoUtil.readToByteArray(new ByteArrayInputStream(pdfOutputStream.toByteArray()));
				return Kv.init().set("bytes",pdfBytes).set("fileName",contractName+".pdf");
			}
		}catch (Exception e){
			bladeLogger.error("生成加工合同","生成加工合同异常:"+ ExceptionUtil.stacktraceToString(e));
		}
		return null;
	}

	private List<FlowProcessingInfoVo> getContractFilesData(FlowProcessingContractItemEntity contractItem){
		List<FlowProcessingInfoVo> res = new ArrayList<>();
		String files = contractItem.getFiles();
		if(StrUtil.isBlank(files)){
			//bladeLogger.error("获取可做加工合同附件","获取可做合同附件files为空"+contractItem.getId());
			return res;
		}
		List<String> split = StrUtil.split(files, "|",true,true);
		if(split.isEmpty())
		{
			//bladeLogger.error("获取可做加工合同附件","获取可做合同附件split为空"+contractItem.getId());
			return res;
		}

		for (String s : split) {
			String fileUrl = ossBuilder.template().fileLink(s);
			try {
				InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(fileUrl));
				FlowProcessingInfoVo info = this.analyzeProcessingXls(inputStream);
				res.add(info);
			}catch (Exception e){
				bladeLogger.error("读取加工清单","读取加工清单【"+contractItem.getId() + "】异常：" + ExceptionUtil.stacktraceToString(e));
			}
		}

		return res;
	}

	/**
	 * 解析加工清单数据
	 * @return
	 */
	public FlowProcessingInfoVo analyzeProcessingXls(InputStream inputStream){
		FlowProcessingInfoVo info = new FlowProcessingInfoVo();
		// 解析加工清单
		EasyExcel.read(inputStream, new ReadProcessXlsListener(info))
			.extraRead(CellExtraTypeEnum.MERGE)//需要合并单元格信息
			.sheet("加工费")
			//.sheet()
			.doRead();
		return info;
	}


	@Override
	public void outputPaymentPdf(Long paymentId, HttpServletRequest request, HttpServletResponse response) {
		if(null == paymentId || paymentId <= 0){
			return;
		}

		String poiTl = "classpath:/poi-tl/payment_apply.docx";	//公用[设计合同、加工合同]
		try{
			Kv paymentKv = Kv.create();
			FlowProcessingContractPaymentEntity payment = flowProcessingContractPaymentService.getById(paymentId);

			Long contractDetailsId = payment.getContractDetailsId();
			FlowProcessingContractDetailsVO flowProcessingContractDetails = this.findById(contractDetailsId);

			String applicantName="",approverName="";
			if(null != payment.getPayInfoId() && payment.getPayInfoId() > 0){
				FlowOutContractPayInfoEntity flowOutContractPayInfo = flowOutContractPayInfoService.getById(payment.getPayInfoId());
				applicantName = flowOutContractPayInfo.getApplicantName();
				approverName = flowOutContractPayInfo.getApproverName();
				payment.setApplicationDate(flowOutContractPayInfo.getCreateTime());
			}

//			// 申请人
//			User applicationUser = null;
//			if(StrUtil.isNotBlank(payment.getApplicationUser())){
//				applicationUser = UserCache.getUser(Long.valueOf(payment.getApplicationUser()));
//			}
//			Dept applicationDept = null;
//
//			// 申请部门
//			if(null != applicationUser) {
//				String deptId = applicationUser.getDeptId();
//				applicationDept = DeptCache.getDept(Long.valueOf(deptId));
//			}
//
//			// 审批人
//			StringBuilder approverUserNames = new StringBuilder();
//			if(StrUtil.isNotBlank(payment.getApproverUser())) {
//				Long[] userIds = Func.toLongArray(payment.getApproverUser());
//				for (Long userId : userIds) {
//					User approverUser = UserCache.getUser(userId);
//					if(null != approverUser){
//						approverUserNames.append(approverUser.getRealName()).append(",");
//					}
//				}
//				if (approverUserNames.length() > 0) {
//					approverUserNames = approverUserNames.deleteCharAt(approverUserNames.length() - 1);
//				}
//			}

			// 乙方公司名称 这里取到签订公司(生产基地)的名字
			String partyBName = FlowProjectEqDetailsEntity.FACTORY_NAME[Integer.parseInt(flowProcessingContractDetails.getPartyB())-1];

			// 申请部门
			String partyAName = FlowProjectEqDetailsEntity.FACTORY_NAME[Integer.parseInt(flowProcessingContractDetails.getPartyA())-1];

			// 金额 大/小写
			BigDecimal contractAmount = payment.getAmount();
			String totalAmountInWords = "";
			String amount = "";
			if(null != contractAmount){
				amount = contractAmount.toString();
				totalAmountInWords = ConvertUpMoney.toChinese(contractAmount);
			}

			paymentKv.set("applicationDate",payment.getApplicationDate());			//申请日期
			paymentKv.set("applicationDepartment",partyAName);	//申请部门
			paymentKv.set("entrustType","加工合同");		//请款类别
			paymentKv.set("payeeName",partyBName);		//收款单位
			paymentKv.set("moneyType",DictBizCache.getValue("money_type",payment.getMoneyType()));			//款项用途
			paymentKv.set("code",flowProcessingContractDetails.getCode());				//合同编号
			paymentKv.set("codeDetails",flowProcessingContractDetails.getContent());	//合同详情
			paymentKv.set("totalAmountInWords",totalAmountInWords);	//付款金额大写
			paymentKv.set("amount",amount);							//付款金额小写
			paymentKv.set("applicantName",applicantName);	//申请人
			paymentKv.set("approverName",approverName);		//审批人

			// 获取 Word 模板所在路径
			Resource resource = ResourceUtil.getResource(poiTl);
			// 配置
			Configure configure = Configure.builder()
				.useSpringEL()
				.build();

			Map<String,Object> data = new HashMap<>();
			data.put("payment", paymentKv);

			// 通过 XWPFTemplate 编译文件并渲染数据到模板中
			XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(),configure).render(data);

			// 生成到word字符流
			ByteArrayOutputStream fos = new ByteArrayOutputStream();
			template.writeAndClose(fos);
			ByteArrayInputStream inputStream = new ByteArrayInputStream(fos.toByteArray());
			fos = null;

			// 生成PDF 输出到页面
			String fileName = "加工合同付款申请单.pdf";
			ServletUtil.setDownloadHeader(request,response,fileName);
			ServletOutputStream outputStream = response.getOutputStream();
			DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, outputStream);
            /*if(null != outputStream){
                try {
                    outputStream.close();
                }finally {
                    outputStream = null;
                }
            }*/
		}catch (Exception e){
			bladeLogger.error("生成加工合同付款申请单异常",ExceptionUtil.stacktraceToString(e));
		}
	}


	/**
	 * 获取流程处理表单数据
	 * @param flow
	 * @return
	 */
	@Override
	public R auditFormDetail(BladeFlow flow) {

		if(StringUtil.isBlank(flow.getBusinessId())){
			return R.fail("businessId为空");
		}

		// 环节编号
		String taskDefKey = flow.getTaskDefinitionKey();
		if(StringUtil.isBlank(taskDefKey)){
			return R.fail("taskDefKey为空");
		}

		Kv model = Kv.init();

		// 合同
		FlowProcessingContractDetailsEntity flowProcessingContractDetails = this.getById(Long.valueOf(flow.getBusinessId()));
		model.set("flowProcessingContractDetails",flowProcessingContractDetails);

		//可做合同
		//FlowProcessingContractEntity flowProcessingContract = flowProcessingContractService.getById(flowProcessingContractDetails.getContractId());
		//可做合同明细
		FlowProcessingContractItemEntity flowProcessingContractItem = flowProcessingContractItemService.getById(flowProcessingContractDetails.getContractItemId());
		FlowProcessingContractItemVO flowProcessingContractItemVo = FlowProcessingContractItemWrapper.build().entityVO(flowProcessingContractItem);
		//可做合同明细的付款信息
		List<FlowProcessingContractPaymentEntity> contractPaymentList = flowProcessingContractPaymentService.lambdaQuery()
			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[0])
			.eq(FlowProcessingContractPaymentEntity::getContractItemId, flowProcessingContractItemVo.getId())
			.list();
		flowProcessingContractItemVo.setFlowProcessingContractPaymentList(contractPaymentList);
		model.set("flowProcessingContractItem",flowProcessingContractItemVo);

		// 已经过了自己参与的环节
		if (flow.isFinishTask()) {
			return R.data(model);
		}

		switch (taskDefKey){
			case "sign_contract"://提交签订合同
			case "reEditContract"://重新修改合同
			case "leaderAudit"://部门领导审批
			case "dgmAudit"://天津工厂常务副总审核
			case "callback_contract"://上传双章合同
				model.set("audit", true);
				break;
		}
		return R.data(model);
	}

	/**
	 * 审核-保存表单数据
	 * @param flowProcessingContractDetails
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public R audit(FlowProcessingContractDetailsVO flowProcessingContractDetails) {
		Map<String, Object> vars = Maps.newHashMap();


		//启动流程
		if (StringUtils.isBlank(flowProcessingContractDetails.getProcInsId())) {
			flowProcessingContractDetails.setProcStatus(FlowProcessingContractDetailsEntity.PROCSTATUS[1]);
			flowProcessingContractDetails.setStatus(Integer.valueOf(FlowProcessingContractDetailsEntity.STATUS[0]));
			super.saveOrUpdate(flowProcessingContractDetails);

			vars.put("signUser", AuthUtil.getUserAccount());
			vars.put("title",flowProcessingContractDetails.getCode());
			flowService.startProcessInstanceByKey(FlowUtil.PD_PROCESSING_CONTRACT_AUDIT[0], FlowUtil.PD_PROCESSING_CONTRACT_AUDIT[1], String.valueOf(flowProcessingContractDetails.getId()), vars);
			LogUtils.info(getClass(),DateUtil.format(new Date(), "yy--MM-dd HH:mm:ss") + "启动加工合同[" + flowProcessingContractDetails.getCode() + "]的流程");
		}else {
			String flag = flowProcessingContractDetails.getFlow().getFlag();
			//当前流程环节标识
			String taskDefKey = flowProcessingContractDetails.getFlow().getTaskDefinitionKey();
			String businessId = flowProcessingContractDetails.getFlow().getBusinessId();
			FlowProcessingContractDetailsEntity entity = this.getById(businessId);

			switch (taskDefKey){
				case "sign_contract"://签订合同
					if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(entity.getPartyA())) {//天津
						vars.put("leaderAuditUser", MachUtils.getManagerByRole("供应链中心副经理"));
					} else {//江苏
						vars.put("leaderAuditUser", MachUtils.getManagerByRole("江苏工厂副总经理"));
					}
					flowProcessingContractDetails.setApproverName(AuthUtil.getNickName());
					//flowProcessingContractDetails.setProcStatus(FlowProcessingContractDetailsEntity.PROCSTATUS[2]);//
					super.saveOrUpdate(flowProcessingContractDetails);
					// 处理交货日期
					updateItemDeliveryDate(flowProcessingContractDetails);
					break;
				case "reEditContract"://重新修改合同信息
					this.reEditContract(flowProcessingContractDetails);
					break;
				case "leaderAudit"://部门领导审批
					if("yes".equals(flag)){
						vars.put("pass",1);
					}else{
						vars.put("pass",0);
						flowProcessingContractDetails.getFlow().setComment("[驳回]"+flowProcessingContractDetails.getFlow().getComment());
					}
					if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(entity.getPartyA())) {//天津
						vars.put("dgmAuditUser", MachUtils.getManagerByRole("天津工厂常务副总"));
					} else {//江苏
						vars.put("dgmAuditUser", MachUtils.getManagerByRole("江苏工厂常务副总"));
					}
					break;
				case "dgmAudit"://常务副总审批
					if("yes".equals(flag)){
						vars.put("pass",1);
					}else{
						vars.put("pass",0);
						flowProcessingContractDetails.getFlow().setComment("[驳回]"+flowProcessingContractDetails.getFlow().getComment());
					}
					flowProcessingContractDetails.setStatus(Integer.valueOf(FlowProcessingContractDetailsEntity.STATUS[1]));
					super.saveOrUpdate(flowProcessingContractDetails);
					break;
				case "callback_contract"://上传双章合同
					flowProcessingContractDetails.setProcStatus(FlowProcessingContractDetailsEntity.PROCSTATUS[2]);
					flowProcessingContractDetails.setStatus(Integer.valueOf(FlowProcessingContractDetailsEntity.STATUS[2]));
					super.saveOrUpdate(flowProcessingContractDetails);

					// 双章合同完成，自动创建（全款/预付款）付款申请单
					List<FlowProcessingContractDetailsEntity> list = new ArrayList<>();
					list.add(flowProcessingContractDetails);
					this.createOnePayment(flowProcessingContractDetails.getId(), list, FlowProjectPayEntity.MONEY_TYPE[1]);

//					// 双章合同完成，将提货款之前阶段的付款申请单状态改为可提交    ！！！这里注释掉，查询的时候动态展示
//					// 顺序为 0:全款,1:预付定金,6:投产定金,7.进度款,2:提货款,5:到货款,3:验收款,4:质保金,
//					List<String> moneyTypes = Arrays.asList(FlowProjectPayEntity.MONEY_TYPE[0],FlowProjectPayEntity.MONEY_TYPE[1],FlowProjectPayEntity.MONEY_TYPE[6],FlowProjectPayEntity.MONEY_TYPE[7]);
//					flowProcessingContractPaymentService.lambdaUpdate()
//						.set(FlowProcessingContractPaymentEntity::getStatus, Integer.valueOf(MachConst.YES.getCode()))
//						.in(FlowProcessingContractPaymentEntity::getMoneyType, moneyTypes)
//						.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, flowProcessingContractDetails.getId())
//						.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
//						.update();

					break;
				default:
					break;
			}
			// 完成审批
			flowService.completeTask(flowProcessingContractDetails.getFlow().getTaskId(), flowProcessingContractDetails.getFlow().getProcessInstanceId(),
				flowProcessingContractDetails.getFlow().getComment(), vars);
		}
		return R.success("保存成功");
	}

	private void updateItemDeliveryDate(FlowProcessingContractDetailsVO flowProcessingContractDetails) {
		String itemDeliveryDate = DateUtil.formatDate(flowProcessingContractDetails.getItemDeliveryDate());
		if (StringUtil.isNotBlank(itemDeliveryDate) && flowProcessingContractDetails.getFlowProcessingContractItem() != null) {
			Long eqDetailsId = flowProcessingContractDetails.getFlowProcessingContractItem().getId();
			if (eqDetailsId != null) {
				flowProcessingContractItemService.lambdaUpdate()
					.set(FlowProcessingContractItemEntity::getItemDeliveryDate, itemDeliveryDate)
					.eq(FlowProcessingContractItemEntity::getId, eqDetailsId).update();
			}
		}
	}

	/**
	 * 创建申请单
	 * @param contractDetailId  合同id
	 * @param list	同一个合同编号下的合同
	 * @param moneyType	款项类型
	 */
	private boolean createOnePayment(Long contractDetailId, List<FlowProcessingContractDetailsEntity> list, String moneyType) {
		if(null == contractDetailId || null == list || list.isEmpty() || StrUtil.isBlank(moneyType)){
			log.error("创建付款单失败");
			return false;
		}

		// 流程启动用户
		String startUserAccount = null;
		Long startUserId = null;
		String startUserDeptId = null;
		// 获取流程启动用户
		String partyA = list.get(0).getPartyA();
		String partyB = list.get(0).getPartyB();
		String roleAliasName = StrUtil.format("processing_payment_{}_{}",partyA,partyB);
		User startUser = userService.getFirstUserByRoleAliasName(roleAliasName);
		if(null != startUser) {
			startUserAccount = startUser.getAccount();
			startUserId = startUser.getId();
			startUserDeptId = startUser.getDeptId();
		}
		if(StrUtil.isBlank(startUserAccount)){
			startUserAccount = AuthUtil.getUserAccount();
			startUserId = AuthUtil.getUserId();
			startUserDeptId = AuthUtil.getDeptId();
		}

		//合同类型
		FlowProcessingContractPaymentEntity flowContractDetailsPayment = new FlowProcessingContractPaymentEntity();
		//flowContractDetailsPayment.setCode(code);
		flowContractDetailsPayment.setContractId(list.get(0).getContractId());
		flowContractDetailsPayment.setContractDetailsId(contractDetailId);
		flowContractDetailsPayment.setContractType(FlowProcessingContractDetailsEntity.CONTRACT_TYPE[0]);//类别
		flowContractDetailsPayment.setType(FlowProcessingContractPaymentEntity.TYPE[2]);//付款单
		//提交流程后会设置，这里不设置了。
		//flowContractDetailsPayment.setStatus(Integer.valueOf(FlowProcessingContractPaymentEntity.STATUS[1]));//将付款单状态设置为审核中
		flowContractDetailsPayment.setCreateUser(startUserId);
		flowContractDetailsPayment.setCreateDept(Func.firstLong(startUserDeptId));
		flowContractDetailsPayment.setUpdateUser(startUserId);

		//首款类型
		String firstMoneyType = moneyType;
		//总价
		BigDecimal totalAmount = BigDecimal.ZERO;
		//合同编号详情
		StringBuilder codeDetails = new StringBuilder();
		List<Long> updateStatusPaymentIds = new ArrayList<>();
		for (FlowProcessingContractDetailsEntity contractDetails : list) {
			long contractDetailsId = contractDetails.getId();

			Long contractId = contractDetails.getContractId();
			FlowProcessingContractEntity contract = flowProcessingContractService.getById(contractId);
			FlowProjectEqDetailsEntity flowProjectEqDetails = flowProjectEqDetailsService.getCache(contract.getEqDetailsId());
			//FlowProjectScheduleEntity flowProjectSchedule = flowProjectScheduleService.getCache(flowProjectEqDetails.getScheduleId());

			//记录详细信息
			if (!codeDetails.isEmpty()) {
				codeDetails.append(" + ");
			}
			codeDetails.append(contractDetails.getSort() + "：" + flowProjectEqDetails.getCode());

			// 全款/预付款，不并存
			if (FlowProjectPayEntity.MONEY_TYPE[1].equals(moneyType)) {
				FlowProcessingContractPaymentEntity paymentEntity = null;

				// 先获取全款的
				paymentEntity = flowProcessingContractPaymentService.lambdaQuery()
					.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
					.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, contractDetailsId)
					.eq(FlowProcessingContractPaymentEntity::getMoneyType, FlowProjectPayEntity.MONEY_TYPE[0])
					.one();
				firstMoneyType = FlowProjectPayEntity.MONEY_TYPE[0];

				// 没有全款类型，再获取预付款的
				if(null == paymentEntity){
					paymentEntity = flowProcessingContractPaymentService.lambdaQuery()
						.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
						.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, contractDetailsId)
						.eq(FlowProcessingContractPaymentEntity::getMoneyType, FlowProjectPayEntity.MONEY_TYPE[1])
						.one();
					firstMoneyType = FlowProjectPayEntity.MONEY_TYPE[1];
				}
				if(null == paymentEntity){
					throw new RuntimeException("找不到付款信息,"+moneyType+","+contractDetailsId+",eqCode:"+flowProjectEqDetails.getCode());
				}
				totalAmount = NumberUtil.add(totalAmount, paymentEntity.getAmount());
				updateStatusPaymentIds.add(paymentEntity.getId());
			}
			// 提货款 or 其他
			else{
				FlowProcessingContractPaymentEntity paymentEntity = flowProcessingContractPaymentService.lambdaQuery()
					.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
					.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, contractDetailsId)
					.eq(FlowProcessingContractPaymentEntity::getMoneyType, moneyType)
					.one();
				if(null == paymentEntity) {
					throw new RuntimeException("找不到付款信息,"+moneyType+","+contractDetailsId+",eqCode:"+flowProjectEqDetails.getCode());
				}
				totalAmount = NumberUtil.add(totalAmount, paymentEntity.getAmount());
				updateStatusPaymentIds.add(paymentEntity.getId());
			}

		}//end for

		flowContractDetailsPayment.setMoneyType(firstMoneyType);
		flowContractDetailsPayment.setAmount(totalAmount);
		flowContractDetailsPayment.setCodeDetails(codeDetails.toString());

		// 不启动流程了，直接设置审批完成
		flowContractDetailsPayment.setStatus(Integer.parseInt(FlowProcessingContractPaymentEntity.TYPE3_STATUS[2]));
		flowProcessingContractPaymentService.save(flowContractDetailsPayment);

		// 更新关联关系
		Long paymentId = flowContractDetailsPayment.getId();//付款单id
		flowProcessingContractPaymentService.lambdaUpdate()
			.set(FlowProcessingContractPaymentEntity::getPaymentId, paymentId)
			.set(FlowProcessingContractPaymentEntity::getStatus, MachConst.NO.getCode())//改成不可提交
			.in(FlowProcessingContractPaymentEntity::getId, updateStatusPaymentIds)//加工合同付款信息ids
			.update();

		// 将对应合同的付款单设置为已付款
		flowProcessingContractPaymentService.setPaied(flowContractDetailsPayment);
		// 审批完成，添加到可做付款申请
		flowProcessingContractPaymentService.addPayInfoReal(flowContractDetailsPayment);

//		// 启动流程
//		FlowProcessingContractPaymentVO flowProcessingContractPaymentVO = FlowProcessingContractPaymentWrapper.build().entityVO(flowContractDetailsPayment);
//		flowProcessingContractPaymentService.audit(flowProcessingContractPaymentVO);


		return true;
	}

	/**
	 * 重新修改合同信息
	 * @param flowProcessingContractDetails
	 */
	private void reEditContract(FlowProcessingContractDetailsVO flowProcessingContractDetails) {
		FlowProcessingContractItemVO flowProcessingContractItem = flowProcessingContractDetails.getFlowProcessingContractItem();

//		String content = flowProcessingContractItem.getContent();
//		String equipmentColor = flowProcessingContractItem.getEquipmentColor();
		String amount = flowProcessingContractItem.getAmount();
//		String payType = flowProcessingContractItem.getPayType();
//		String contractFiles = flowProcessingContractItem.getFiles();
		List<FlowProcessingContractPaymentEntity> flowProcessingContractPaymentList = flowProcessingContractItem.getFlowProcessingContractPaymentList();

		// 1.更新可做合同明细信息
		flowProcessingContractItemService.saveOrUpdate(flowProcessingContractItem);
//		flowProcessingContractItemService.lambdaUpdate()
//			.set(FlowProcessingContractItemEntity::getContent, content)
//			.set(FlowProcessingContractItemEntity::getEquipmentColor, equipmentColor)
//			.set(FlowProcessingContractItemEntity::getAmount, amount)
//			.set(FlowProcessingContractItemEntity::getFiles, contractFiles)
//			.set(FlowProcessingContractItemEntity::getPayType, payType)
//			.eq(FlowProcessingContractItemEntity::getId, flowProcessingContractDetails.getContractItemId())
//			.update();

		// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓更新可付款合同产品的付款信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
		// 1.1删除原来的付款信息
		flowProcessingContractPaymentService.lambdaUpdate()
			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[0])
			.eq(FlowProcessingContractPaymentEntity::getContractItemId, flowProcessingContractDetails.getContractItemId())
			.remove();
		// 1.2加入新的付款信息
		for (int i = 0; i < flowProcessingContractPaymentList.size(); i++) {
			FlowProcessingContractPaymentEntity flowProcessingContractPayment = flowProcessingContractPaymentList.get(i);
			flowProcessingContractPayment.setId(null);
			flowProcessingContractPayment.setType(FlowProcessingContractPaymentEntity.TYPE[0]);
			flowProcessingContractPayment.setContractId(flowProcessingContractDetails.getContractId());
			flowProcessingContractPayment.setContractItemId(flowProcessingContractDetails.getContractItemId());
			flowProcessingContractPayment.setSort(String.valueOf(i+1));
			// 算出百分比
			BigDecimal a = NumberUtil.mul(new BigDecimal(amount),new BigDecimal(flowProcessingContractPayment.getProportion()));
			BigDecimal b = NumberUtil.div(a,new BigDecimal("100"));
			flowProcessingContractPayment.setAmount(b);
			flowProcessingContractPaymentService.save(flowProcessingContractPayment);
		}

		// 2.更新合同信息
		this.lambdaUpdate()
			.set(FlowProcessingContractDetailsEntity::getUnitPrice, amount)
			.eq(FlowProcessingContractDetailsEntity::getId, flowProcessingContractDetails.getId())
			.update();

		// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓更新合同的付款信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
		//删除原来的付款信息
		flowProcessingContractPaymentService.lambdaUpdate()
			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[1])
			.eq(FlowProcessingContractPaymentEntity::getContractDetailsId, flowProcessingContractDetails.getId())
			.remove();

		// 加入新的付款信息
		List<FlowProcessingContractPaymentEntity> paymentList2 = flowProcessingContractPaymentService.lambdaQuery()
			.eq(FlowProcessingContractPaymentEntity::getType, FlowProcessingContractPaymentEntity.TYPE[0])
			.eq(FlowProcessingContractPaymentEntity::getContractItemId, flowProcessingContractDetails.getContractItemId())
			.list();
		for (FlowProcessingContractPaymentEntity payment2 : paymentList2) {
			payment2.setId(null);
			payment2.setType(FlowProcessingContractPaymentEntity.TYPE[1]);
			payment2.setContractDetailsId(flowProcessingContractDetails.getId());
			payment2.setStatus(Integer.valueOf(MachConst.YES.getCode()));//是否可提交申请单
			// 算出百分比
			BigDecimal a = NumberUtil.mul(new BigDecimal(amount),new BigDecimal(payment2.getProportion()));
			BigDecimal amount2 = NumberUtil.div(a,new BigDecimal("100"));
			payment2.setAmount(amount2);
			payment2.setPaymentStatus(MachConst.NO.getCode());
			flowProcessingContractPaymentService.save(payment2);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public R createPayment(List<Long> ids, String type) {
		// 这里可能选择多个合同编号的合同，要根据合同编号分组，然后创建付款申请单 (几个合同就生成几个付款单)

		// 取出所有合同详情
		for (int i = 0; i < ids.size(); i++) {
			Long id = ids.get(i);

			FlowProcessingContractDetailsEntity contractDetails = this.getById(id);
			List<FlowProcessingContractDetailsEntity> list = new ArrayList<>();
			list.add(contractDetails);

			this.createOnePayment(id,list,type);
		}
		return R.success("创建成功");
	}

	/*@Override
	@Transactional(readOnly = false)
	public R createPayment1(List<Long> idList, List<String> moneyTypeList) {
		if(idList.size() != moneyTypeList.size()){
			return R.fail("创建失败，参数不一致");
		}

		//多个合同
		for (int i = 0; i < idList.size(); i++) {
			Long id = idList.get(i);
			String moneyType = moneyTypeList.get(i);
			FlowProcessingContractDetailsEntity flowProcessingContractDetails = this.getById(id);
			Long contractId = flowProcessingContractDetails.getContractId();
			FlowProcessingContractPaymentEntity flowProcessingContractPayment = flowProcessingContractPaymentService.getPayment(FlowProcessingContractPaymentEntity.TYPE[0], contractId, moneyType);
			flowProcessingContractPayment.setId(null);
			flowProcessingContractPayment.setType(FlowProcessingContractPaymentEntity.TYPE[1]);
			flowProcessingContractPayment.setContractDetailsId(flowProcessingContractDetails.getId());
			flowProcessingContractPayment.setStatus(Integer.valueOf(FlowProcessingContractPaymentEntity.STATUS[0]));

			// 这里用单台设备金额乘比例 公式 = 单套设备金额（万元） * 10000元 * 比例%
			// 举例：100 * 10000 * 105 / 100
			String proportionStr = flowProcessingContractPayment.getProportion();
			FlowProcessingContractEntity flowProcessingContract = flowProcessingContractService.getById(contractId);
			FlowProjectEqDetailsEntity flowProjectEqDetails = flowProjectEqDetailsService.getById(flowProcessingContract.getEqDetailsId());
			FlowProjectDetailsEntity flowProjectDetails = flowProjectDetailsService.getById(flowProjectEqDetails.getPdId());
			String unitPrice = flowProjectDetails.getUnitPrice();//单台设备价格 万元
			String amount = "0";
			if(StrUtil.isNotBlank(proportionStr) && StrUtil.isNotBlank(unitPrice)){
				Double basePrice = Double.valueOf(unitPrice);
				Double proportion = Double.valueOf(proportionStr);
				Double proportionNum = BigDecimalUtils.div(proportion, 100d,4);
				double basePriceYuan = BigDecimalUtils.mul(basePrice, 10000d);
				amount = String.valueOf(BigDecimalUtils.mul(basePriceYuan, proportionNum));
			}
			flowProcessingContractPayment.setAmount(new BigDecimal(amount));

			flowProcessingContractPaymentService.save(flowProcessingContractPayment);
		}
		return R.success("创建成功");
	}*/

	/**
	 * 获取合同数量
	 * @param flowProcessingContractDetails
	 * @return
	 */
	@Override
	public List<FlowProcessingContractDetailsVO> getContractCountList(FlowProcessingContractDetailsVO flowProcessingContractDetails) {
		return baseMapper.getContractCountList(flowProcessingContractDetails);
	}

    @Override
    public FlowProcessingContractDetailsEntity findByProcInsId(String procInsId) {
		FlowProcessingContractDetailsEntity contractDetail = CacheUtil.get("processContractDetailProcIns","id:", procInsId,FlowProcessingContractDetailsEntity.class,false);
		if (contractDetail != null) {
			return contractDetail;
		} else {
			FlowProcessingContractDetailsEntity where = new FlowProcessingContractDetailsEntity();
			where.setProcInsId(procInsId);
			contractDetail = baseMapper.selectOne(Condition.getQueryWrapper(where));
			if(null != contractDetail){
				CacheUtil.put("processContractDetailProcIns", "id:",contractDetail.getId(), contractDetail,false);
			}
			return contractDetail;
		}
    }

	/**
	 * 自动启动委外加工合同质检流程
	 */
	@Override
	@Transactional(readOnly = false)
	public void autoStartContractQc() {
		// 交货期前15天发起[外协合同验收单]流程
		// 查出符合条件的合同付款单
		List<FlowProcessingContractPaymentVO> list = flowProcessingContractPaymentService.getCanQcContractPayment();
		for (FlowProcessingContractPaymentVO vo : list) {
			// 检查是否发起过外协验收单，发起过就不发了
			Long count = flowProcessingContractQcService.lambdaQuery()
				.eq(ProcessingContractQcEntity::getContractItemId, vo.getContractItemId())
				.count();
			if(null != count && count > 0){
				continue;
			}

			// 启动流程
			ProcessingContractQcEntity qc = new ProcessingContractQcEntity();
			qc.setContractId(vo.getContractId());
			qc.setContractItemId(vo.getContractItemId());
			Long contractDetailsId = vo.getContractDetailsId();//加工合同详情id
			FlowProcessingContractDetailsEntity byId = flowProcessingContractDetailsService.getById(contractDetailsId);
			qc.setCode(byId.getCode());
			qc.setPartyA(byId.getPartyA());
			qc.setContractDetailsId(vo.getContractDetailsId());
			qc.setPartyB(byId.getPartyB());
			qc.setStatus(Integer.valueOf(MachConst.NO.getCode()));
			flowProcessingContractQcService.save(qc);
			flowProcessingContractQcService.audit(qc);
		}
	}

	/**
	 * 修改指定字段数据
	 * @param dto
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public R updateData(FlowProcessingContractDetailsDTO dto) {
		Long id = dto.getId();
		String column = dto.getColumn();
		if(id == null || StrUtil.isBlank(column)){
			return R.fail("更新失败");
		}

		LambdaUpdateChainWrapper<FlowProcessingContractDetailsEntity> lambdaUpdateWrapper = this.lambdaUpdate();

		// 质保金起算时间
		if(StrUtil.equals("p4StartDate",column)){
			lambdaUpdateWrapper.set(FlowProcessingContractDetailsEntity::getP4StartDate, dto.getP4StartDate());
		}
		// 发票日期
		else if(StrUtil.equals("invoiceDate",column)){
			lambdaUpdateWrapper.set(FlowProcessingContractDetailsEntity::getInvoiceDate, dto.getInvoiceDate());
		}
		// 发票金额
		else if(StrUtil.equals("invoiceAmount",column)){
			lambdaUpdateWrapper.set(FlowProcessingContractDetailsEntity::getInvoiceAmount, dto.getInvoiceAmount());
		}
		// 发票号
		else if(StrUtil.equals("invoiceNo",column)){
			lambdaUpdateWrapper.set(FlowProcessingContractDetailsEntity::getInvoiceNo, dto.getInvoiceNo());
		}else{
			return R.fail("更新失败");
		}

		boolean result = lambdaUpdateWrapper.eq(FlowProcessingContractDetailsEntity::getId,dto.getId()).update();
		return R.status(result);
	}


	/**
	 * 设置关联加工合同的质保金起算时间
	 * @param projectDetailId
	 * @param planDate			启动发货日
	 */
	@Override
	@Transactional(readOnly = false)
	public void updateQaStartDate(Long projectDetailId, Date planDate) {
		if(null == projectDetailId || null == planDate){
			log.error("更新质保金失败："+projectDetailId+":"+planDate);
			return;
		}
		// 质保金起算时间 = 启动发货日+12个月
		DateTime p4StartDate = cn.hutool.core.date.DateUtil.offset(planDate, DateField.MONTH, 12);
		baseMapper.updateP4StartDate(projectDetailId,p4StartDate);
	}

	@Override
	public List<Long> getCanPayContractDetailIds(String moneyType) {
		return baseMapper.getCanPayContractDetailIds(moneyType);
	}
}
