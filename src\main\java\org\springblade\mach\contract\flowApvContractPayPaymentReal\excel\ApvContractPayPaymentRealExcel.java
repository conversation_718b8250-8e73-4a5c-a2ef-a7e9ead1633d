/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayPaymentReal.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 付款单与款单关联表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ApvContractPayPaymentRealExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 付款方
	 */
	@ColumnWidth(20)
	@ExcelProperty("付款方")
	private String partyA;
	/**
	 * 收款方
	 */
	@ColumnWidth(20)
	@ExcelProperty("收款方")
	private String partyB;
	/**
	 * 合同编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("合同编号")
	private String contractCode;
	/**
	 * 单套产品合同总额
	 */
	@ColumnWidth(20)
	@ExcelProperty("单套产品合同总额")
	private String eqAmount;
	/**
	 * 单套产品付款金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("单套产品付款金额")
	private String eqPaymentAmount;
	/**
	 * 付款单ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("付款单ID")
	private Long payInfoId;
	/**
	 * 备注信息
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注信息")
	private String remarks;
	/**
	 * 合同文件
	 */
	@ColumnWidth(20)
	@ExcelProperty("合同文件")
	private String contractFiles;
	/**
	 * 附件文件
	 */
	@ColumnWidth(20)
	@ExcelProperty("附件文件")
	private String attachmentFiles;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}