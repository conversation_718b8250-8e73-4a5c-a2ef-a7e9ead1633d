<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <!-- 右侧行内菜单 -->
      <template #menu="{row}">
          <el-button
              type="primary"
              size="small"
              text
              @click="handleDetailApvPayInfo(row)"
          >详情
          </el-button>
      </template>

      <!-- 列表：编号 -->
      <template #code="{row}">
        <el-link type="primary" @click="handleDetailApvPayInfo(row);">{{row.code}}</el-link>
      </template>

      <template #menu-left>
        <el-button type="primary"
                   icon="el-icon-plus"
                   plain
                   v-if="permission.apvContractPayInfo_add"
                   @click="handleCreate">创建付款审批单
        </el-button>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.apvContractPayInfo_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button>
      </template>
    </avue-crud>

    <!-- 创建付款审批单弹窗 -->
    <el-dialog
      title="创建付款审批单"
      v-model="payInfoFormBox.open"
      width="90%"
      destroy-on-close
      append-to-body
      :close-on-click-modal="false"
    >
      <apvContractPayInfoForm
        :partyA="payInfoFormBox.partyA"
        ref="payInfoFormRef"
        @handleCancel="handlePayInfoFormCancel"
      />
    </el-dialog>

    <!-- 付款审批单详情 -->
    <el-dialog :title="apvPayInfoDetailBox.title" append-to-body v-model="apvPayInfoDetailBox.open" destroy-on-close  width="80%" top="1%">
        <apvContractPayInfoDetails v-if="apvPayInfoDetailBox.open" :businessId="apvPayInfoDetailBox.id" @handleCancel="()=>{apvPayInfoDetailBox.open = false; refreshChange();}"/>
    </el-dialog>

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import option from "@/option/mach/pm/apvContractPayInfo/apvContractPayInfo";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  import apvContractPayInfoForm from "./apvContractPayInfo-form.vue";
  import apvContractPayInfoDetails from "./apvContractPayInfoDetails.vue";

  export default {
    components: {
      apvContractPayInfoForm,
      apvContractPayInfoDetails
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          ...option,
          column: option.column.map(col => {
            if (col.prop === 'code') {
              return {
                ...col,
                slot: true // 启用插槽
              };
            }
            return col;
          }),
          // 确保avue-crud的menu配置为true以显示行内操作按钮，如果已有自定义menu插槽，则不需要此项
          // menu: true, 
        },
        data: [],
        payInfoFormBox: {
          open: false,
          title: "创建付款审批单",
          partyA: "3" // 默认北京
        },
        // 付款审批单详情弹窗
        apvPayInfoDetailBox: {
          open: false,
          id: null,
          title: "付款审批单详情"
        }
      };
    },
    created() {
      // payInfoFormBox is initialized in data()
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.apvContractPayInfo_add, false),
          viewBtn: this.validData(this.permission.apvContractPayInfo_view, false),
          delBtn: this.validData(this.permission.apvContractPayInfo_delete, false),
          editBtn: this.validData(this.permission.apvContractPayInfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
    },
    methods: {
      handleDetailApvPayInfo(row) {
        if (row && row.id) {
            this.apvPayInfoDetailBox.id = row.id;
            this.apvPayInfoDetailBox.open = true;
        } else {
            this.$message.error("无法打开详情：行数据或ID无效。");
            console.error("Invalid row data for details view:", row);
        }
      },
      rowSave(row, done, loading) {
        console.log("新增：",row);
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        console.log("编辑：",row);
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/blade-apvContractPayInfo/apvContractPayInfo/export-apvContractPayInfo?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `付款审批${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },

      // Modified beforeOpen method
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          // Avue-crud sets this.form with the row data before calling beforeOpen for 'edit'/'view'.
          // So, this.form.id should be available.
          if (!this.form || !this.form.id) {
             this.$message.warning('无法加载详情：数据ID缺失。');
             done(); // Call done as we are returning early
             return;
          }
          getDetail(this.form.id).then(res => {
            const responseData = res.data; // Full response data part
            if (responseData && responseData.data && typeof responseData.data === 'object') {
              this.form = responseData.data;
            } else {
              this.$message.error((responseData && responseData.msg) || '获取表单详情失败: 返回格式不正确');
              console.warn("Unexpected response structure from getDetail in beforeOpen:", responseData);
              // Optionally reset form: this.form = {};
            }
            done(); // IMPORTANT: Call done() inside then() after processing
          }).catch(error => {
            console.error("Error in beforeOpen getDetail:", error);
            this.$message.error('获取表单详情失败: 请求处理异常');
            done(); // IMPORTANT: Call done() inside catch() as well
          });
        } else {
          done(); // For 'add' type, or if no async operation, call done() synchronously
        }
      },

      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      // 创建付款审批单
      handleCreate() {
        this.payInfoFormBox.open = true;
      },
      // 付款审批单表单取消
      handlePayInfoFormCancel(flag) {
        this.payInfoFormBox.open = false;
        if (flag) {
          this.refreshChange();
        }
      },

      // Modified onLoad method
      onLoad(page, queryParams = {}) { // Renamed params to queryParams for clarity
        this.loading = true;
        // queryParams will be `this.query` (from refreshChange) or search params (from searchChange/initial load)
        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const responseData = res.data; // Get the full response part, e.g., { code, msg, data: { records, total } }
          // Add robust checking for the expected data structure
          if (responseData && responseData.data && typeof responseData.data === 'object') {
            const data = responseData.data; // This is the { records, total, ... } part
            this.data = data.records || []; // Assign records to table data, default to empty array if records is undefined/null
            this.page.total = data.total || 0; // Assign total, default to 0 if total is undefined/null
          } else {
            // Handle cases where responseData.data is not as expected (e.g., null, not an object, or missing)
            this.data = []; // Reset table data
            this.page.total = 0; // Reset total count
            this.$message.error((responseData && responseData.msg) || '获取列表数据失败: 返回格式不正确');
            // Optional: log the unexpected structure for debugging
            console.warn("Unexpected response structure from getList:", responseData);
          }
        }).catch(error => {
          console.error("Error fetching list:", error);
          this.$message.error('获取列表数据失败: 请求处理异常');
          this.data = []; // Reset data on error
          this.page.total = 0; // Reset total on error
        }).finally(() => {
          this.loading = false; // Ensure loading is always set to false
        });
      },
    }
  };
</script>

<style>
</style>
