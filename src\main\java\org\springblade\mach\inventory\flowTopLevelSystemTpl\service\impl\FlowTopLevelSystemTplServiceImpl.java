package org.springblade.mach.inventory.flowTopLevelSystemTpl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.mapper.FlowTopLevelSystemTplMapper;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.entity.FlowTopLevelSystemTplEntity;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.vo.FlowTopLevelSystemTplVO;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.service.IFlowTopLevelSystemTplService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 顶层系统标准表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
public class FlowTopLevelSystemTplServiceImpl extends BaseServiceImpl<FlowTopLevelSystemTplMapper, FlowTopLevelSystemTplEntity> implements IFlowTopLevelSystemTplService {

    @Override
    public IPage<FlowTopLevelSystemTplVO> selectFlowTopLevelSystemTplPage(IPage<FlowTopLevelSystemTplVO> page, FlowTopLevelSystemTplVO flowTopLevelSystemTpl) {
        return baseMapper.selectFlowTopLevelSystemTplPage(page, flowTopLevelSystemTpl);
    }

	@Override
	public List<FlowTopLevelSystemTplEntity> dicList() {
		List<FlowTopLevelSystemTplEntity> list = this.lambdaQuery()
			.select(FlowTopLevelSystemTplEntity::getId, FlowTopLevelSystemTplEntity::getSystemName)
			.orderByAsc(FlowTopLevelSystemTplEntity::getSortOrder)
			.list();
		return list;
	}
}
