/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmTechSpec.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.crm.crmTechSpec.pojo.entity.CrmTechSpecEntity;
import org.springblade.mach.crm.crmTechSpec.pojo.vo.CrmTechSpecVO;
import org.springblade.mach.crm.crmTechSpec.excel.CrmTechSpecExcel;
import org.springblade.mach.crm.crmTechSpec.wrapper.CrmTechSpecWrapper;
import org.springblade.mach.crm.crmTechSpec.service.ICrmTechSpecService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 技术参数 控制器
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-crmTechSpec/crmTechSpec")
@Tag(name = "技术参数", description = "技术参数接口")
public class CrmTechSpecController extends BladeController {

	private final ICrmTechSpecService crmTechSpecService;

	/**
	 * 技术参数 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入crmTechSpec")
	public R<CrmTechSpecVO> detail(CrmTechSpecEntity crmTechSpec) {
		CrmTechSpecEntity detail = crmTechSpecService.getOne(Condition.getQueryWrapper(crmTechSpec));
		return R.data(CrmTechSpecWrapper.build().entityVO(detail));
	}
	/**
	 * 技术参数 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入crmTechSpec")
	public R<IPage<CrmTechSpecVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> crmTechSpec, Query query) {
		IPage<CrmTechSpecEntity> pages = crmTechSpecService.page(Condition.getPage(query), Condition.getQueryWrapper(crmTechSpec, CrmTechSpecEntity.class));
		return R.data(CrmTechSpecWrapper.build().pageVO(pages));
	}

	/**
	 * 通过项目信息id获取技术参数
	 */
	@GetMapping("/getCrmTechSpecByProjectInfoId")
	public R<List<CrmTechSpecEntity>> getCrmTechSpecByProjectInfoId(@RequestParam Long projectInfoId) {
		List<CrmTechSpecEntity> list = crmTechSpecService.getCrmTechSpecByProjectInfoId(projectInfoId);
		return R.data(list);
	}

	/**
	 * 技术参数 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入crmTechSpec")
	public R<IPage<CrmTechSpecVO>> page(CrmTechSpecVO crmTechSpec, Query query) {
		IPage<CrmTechSpecVO> pages = crmTechSpecService.selectCrmTechSpecPage(Condition.getPage(query), crmTechSpec);
		return R.data(pages);
	}

	/**
	 * 技术参数 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入crmTechSpec")
	public R save(@Valid @RequestBody CrmTechSpecEntity crmTechSpec) {
		return R.status(crmTechSpecService.save(crmTechSpec));
	}

	/**
	 * 技术参数 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入crmTechSpec")
	public R update(@Valid @RequestBody CrmTechSpecEntity crmTechSpec) {
		return R.status(crmTechSpecService.updateById(crmTechSpec));
	}

	/**
	 * 技术参数 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入crmTechSpec")
	public R submit(@Valid @RequestBody CrmTechSpecEntity crmTechSpec) {
		return R.status(crmTechSpecService.saveOrUpdate(crmTechSpec));
	}

	/**
	 * 技术参数 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(crmTechSpecService.removeByIds(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-crmTechSpec")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入crmTechSpec")
	public void exportCrmTechSpec(@Parameter(hidden = true) @RequestParam Map<String, Object> crmTechSpec, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<CrmTechSpecEntity> queryWrapper = Condition.getQueryWrapper(crmTechSpec, CrmTechSpecEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(CrmTechSpec::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(CrmTechSpecEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<CrmTechSpecExcel> list = crmTechSpecService.exportCrmTechSpec(queryWrapper);
		ExcelUtil.export(response, "技术参数数据" + DateUtil.time(), "技术参数数据表", list, CrmTechSpecExcel.class);
	}

}
