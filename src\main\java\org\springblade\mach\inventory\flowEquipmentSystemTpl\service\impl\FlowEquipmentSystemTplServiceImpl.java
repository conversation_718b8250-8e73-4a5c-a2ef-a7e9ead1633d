/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowEquipmentSystemTpl.service.impl;

import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.entity.FlowEquipmentSystemTplEntity;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.vo.FlowEquipmentSystemTplVO;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.excel.FlowEquipmentSystemTplExcel;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.mapper.FlowEquipmentSystemTplMapper;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.service.IFlowEquipmentSystemTplService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备系统标准表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Service
public class FlowEquipmentSystemTplServiceImpl extends BaseServiceImpl<FlowEquipmentSystemTplMapper, FlowEquipmentSystemTplEntity> implements IFlowEquipmentSystemTplService {

	@Override
	public IPage<FlowEquipmentSystemTplVO> selectFlowEquipmentSystemTplPage(IPage<FlowEquipmentSystemTplVO> page, FlowEquipmentSystemTplVO flowEquipmentSystemTpl) {
		return page.setRecords(baseMapper.selectFlowEquipmentSystemTplPage(page, flowEquipmentSystemTpl));
	}


	@Override
	public List<FlowEquipmentSystemTplExcel> exportFlowEquipmentSystemTpl(Wrapper<FlowEquipmentSystemTplEntity> queryWrapper) {
		List<FlowEquipmentSystemTplExcel> flowEquipmentSystemTplList = baseMapper.exportFlowEquipmentSystemTpl(queryWrapper);
		//flowEquipmentSystemTplList.forEach(flowEquipmentSystemTpl -> {
		//	flowEquipmentSystemTpl.setTypeName(DictCache.getValue(DictEnum.YES_NO, FlowEquipmentSystemTpl.getType()));
		//});
		return flowEquipmentSystemTplList;
	}

	/**
	 * 获取顶级设备类型关联的设备系统
	 * @param topLevelId	设备类型id
	 * @return
	 */
	@Override
	public List<FlowEquipmentSystemTplVO> getEquipmentSystemList(Long topLevelId) {
		if(null == topLevelId){
			return new ArrayList<>();
		}
		return baseMapper.getEquipmentSystemList(topLevelId);
	}

}
