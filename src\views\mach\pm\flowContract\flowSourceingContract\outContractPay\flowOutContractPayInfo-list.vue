<template>
  <div><!--basic-container-->
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">

      <template #menu-left>
        <!-- <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.flowOutContractPayInfo_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button> -->
        <!-- <el-button type="primary"
                   icon="el-icon-plus"
                   @click="createPayInfo">创建付款单
        </el-button> -->
      </template>

      <!-- 右侧行内菜单 -->
      <template #menu="{row}">
          <el-button
              type="primary"
              size="small"
              text
              @click="handleDetailPayInfo(row)"
          >详情
          </el-button>
          <el-button
              type="primary"
              size="small"
              text
              @click="handleEditPayInfo(row)"
              v-if="permission.flowOutContractPayInfo_edit"
          >编辑
          </el-button>
      </template>
      
      <!-- 列表：编号 -->
      <template #code="{row}">
        <el-link type="primary" @click="handleDetailPayInfo(row);">{{row.code}}</el-link>
      </template>

    </avue-crud>

    <!-- 付款单详情 -->
    <el-dialog :title="payInfoFormBox.title" append-to-body v-model="payInfoFormBox.open" destroy-on-close  width="80%" top="1%">
        <template v-if="payInfoFormBox.type == 'detail'">
          <flowOutContractPayInfoDetail :businessId="payInfoFormBox.id" @handleCancel="(refresh)=>{payInfoFormBox.open = false;if(refresh){refreshChange();}}"/>
        </template>
    </el-dialog>
    
  </div>
</template>

<script>
  import {getListPage, getDetail, add, update, remove} from "@/api/mach/pm/flowOutContractPayInfo/flowOutContractPayInfo";
  //import option from "@/option/flowOutContractPayInfo/flowOutContractPayInfo";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  //import flowOutContractPayInfoDetail from "./flowOutContractPayInfo-detail.vue";
  import flowOutContractPayInfoDetail from "@/views/work/process/mach/flowContract/flowOutContractPayInfo/flowOutContractPayInfoDetails.vue";
  

  export default {
    components:{
      flowOutContractPayInfoDetail,
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 180,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          searchBtn: true,
          columnBtn: false,
          gridBtn: false,
          selection: true,
          menu: false,
          dialogClickModal: false,
          column: [
            {
              label: "主键ID",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "编号",
              prop: "code",
              type: "input",
              search: true,
              searchSpan: 4,
            },
            {
              label: "付款方",
              prop: "partyA",
              type: "select",
              dicUrl: "/blade-system/dict-biz/dictionary?code=factory_name",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
              searchSpan: 4,
              display: false,
              hide: true,
            },
            {
              label: "收款方",
              prop: "partyB",
              type: "select",
              dicUrl: "/blade-system/dict-biz/dictionary?code=factory_name",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
              searchSpan: 4,
              display: false,
              hide: true,
            },
            {
              label: "收款单位",
              prop: "payee",
              type: "input",
            },
            // {
            //   label: "单位简称",
            //   prop: "payeeSimple",
            //   type: "input",
            // },
            // {
            //   label: "紧急程度",
            //   prop: "level",
            //   type: "input",
            // },
            // {
            //   label: "款项用途",
            //   prop: "fundsUse",
            //   type: "input",
            // },
            // {
            //   label: "支出说明",
            //   prop: "payDesc",
            //   type: "input",
            // },
            {
              label: "付款金额",
              prop: "amount",
              type: "input",
            },
            // {
            //   label: "银行信息是否变更",
            //   prop: "bankChange",
            //   type: "input",
            // },
            // {
            //   label: "开户行",
            //   prop: "bankAddress",
            //   type: "input",
            // },
            // {
            //   label: "账号",
            //   prop: "bankAccount",
            //   type: "input",
            // },
            // {
            //   label: "是否开具发票",
            //   prop: "invoiceStatus",
            //   type: "input",
            // },
            // {
            //   label: "发票备注",
            //   prop: "invoiceRemark",
            //   type: "input",
            // },
            // {
            //   label: "货物是否入库",
            //   prop: "instoreStatus",
            //   type: "input",
            // },
            // {
            //   label: "入库备注",
            //   prop: "instoreRemark",
            //   type: "input",
            // },
            // {
            //   label: "支付方式说明",
            //   prop: "paytypeDesc",
            //   type: "input",
            // },
            {
              label: "付款状态",
              prop: "status",
              type: "select",
              dicData: [
                //0：未付款，1：已付款
                {label: '未付款', value: 0},
                {label: '已付款', value: 1},
              ],
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: false,
              search: true,
              searchSpan: 4,
            },
            // {
            //   label: "审批人",
            //   prop: "approverName",
            //   type: "input",
            // },
            // {
            //   label: "备注信息",
            //   prop: "remarks",
            //   type: "input",
            // },
          ]

        },
        data: [],
        // 编辑付款单
        payInfoFormBox: {
          open: false,
          id: 0,
          type: "add",
          otherParams:{
              multiple:true
          },
          onSelected: res=>{}
        },
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.flowOutContractPayInfo_add, false),
          viewBtn: this.validData(this.permission.flowOutContractPayInfo_view, false),
          delBtn: this.validData(this.permission.flowOutContractPayInfo_delete, false),
          editBtn: this.validData(this.permission.flowOutContractPayInfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      
      handleEditPayInfo(row){
        this.payInfoFormBox.title = "编辑付款单";
        this.payInfoFormBox.type = "edit";
        this.payInfoFormBox.id = row.id;
        this.payInfoFormBox.open = true;
      },
      handleDetailPayInfo(row){
        this.payInfoFormBox.title = "付款单详情";
        this.payInfoFormBox.type = "detail";
        this.payInfoFormBox.id = row.id;
        this.payInfoFormBox.open = true;
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/blade-flowOutContractPayInfo/flowOutContractPayInfo/export-flowOutContractPayInfo?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `外协合同付款单${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;

        const {
        } = this.query;

        let values = {
        };

        getListPage(page.currentPage, page.pageSize, values).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>