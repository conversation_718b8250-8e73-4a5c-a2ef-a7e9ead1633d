/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowConsumableCost.service.impl;

import org.springblade.mach.inventory.flowConsumableCost.pojo.entity.FlowConsumableCostEntity;
import org.springblade.mach.inventory.flowConsumableCost.pojo.vo.FlowConsumableCostVO;
import org.springblade.mach.inventory.flowConsumableCost.excel.FlowConsumableCostExcel;
import org.springblade.mach.inventory.flowConsumableCost.mapper.FlowConsumableCostMapper;
import org.springblade.mach.inventory.flowConsumableCost.service.IFlowConsumableCostService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 耗材成本配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class FlowConsumableCostServiceImpl extends BaseServiceImpl<FlowConsumableCostMapper, FlowConsumableCostEntity> implements IFlowConsumableCostService {

	@Override
	public IPage<FlowConsumableCostVO> selectFlowConsumableCostPage(IPage<FlowConsumableCostVO> page, FlowConsumableCostVO flowConsumableCost) {
		return page.setRecords(baseMapper.selectFlowConsumableCostPage(page, flowConsumableCost));
	}


	@Override
	public List<FlowConsumableCostExcel> exportFlowConsumableCost(Wrapper<FlowConsumableCostEntity> queryWrapper) {
		List<FlowConsumableCostExcel> flowConsumableCostList = baseMapper.exportFlowConsumableCost(queryWrapper);
		//flowConsumableCostList.forEach(flowConsumableCost -> {
		//	flowConsumableCost.setTypeName(DictCache.getValue(DictEnum.YES_NO, FlowConsumableCost.getType()));
		//});
		return flowConsumableCostList;
	}

}
