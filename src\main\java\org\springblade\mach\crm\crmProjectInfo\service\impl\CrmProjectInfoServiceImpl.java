/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmProjectInfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.common.constant.DsKey;
import org.springblade.common.utils.MachUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.mach.crm.crmProjectInfo.excel.CrmProjectInfoExcel;
import org.springblade.mach.crm.crmProjectInfo.mapper.CrmProjectInfoMapper;
import org.springblade.mach.crm.crmProjectInfo.pojo.entity.CrmProjectInfoEntity;
import org.springblade.mach.crm.crmProjectInfo.pojo.vo.CrmProjectInfoVO;
import org.springblade.mach.crm.crmProjectInfo.service.ICrmProjectInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@DS(DsKey.CRM)
@Service
public class CrmProjectInfoServiceImpl extends BaseServiceImpl<CrmProjectInfoMapper, CrmProjectInfoEntity> implements ICrmProjectInfoService {

	@Override
	public IPage<CrmProjectInfoVO> selectCrmProjectInfoPage(IPage<CrmProjectInfoVO> page, CrmProjectInfoVO crmProjectInfo) {
		return page.setRecords(baseMapper.selectCrmProjectInfoPage(page, crmProjectInfo));
	}


	@Override
	public List<CrmProjectInfoExcel> exportCrmProjectInfo(Wrapper<CrmProjectInfoEntity> queryWrapper) {
		List<CrmProjectInfoExcel> crmProjectInfoList = baseMapper.exportCrmProjectInfo(queryWrapper);
		//crmProjectInfoList.forEach(crmProjectInfo -> {
		//	crmProjectInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, CrmProjectInfo.getType()));
		//});
		return crmProjectInfoList;
	}

	@Override
	public R<String> getNextCode() {
		// 获取当年当月编号最大的
		Integer maxCode = baseMapper.getMaxCode();
		maxCode = maxCode == null ? 0 : maxCode;
		String code = MachUtils.headAdd((maxCode + 1) + "", 3, "0");
		String nextCode = DateUtil.format(DateUtil.now(),"yyyy-MM") + "-" + code;
		return R.data(nextCode,"获取成功");
	}

}
