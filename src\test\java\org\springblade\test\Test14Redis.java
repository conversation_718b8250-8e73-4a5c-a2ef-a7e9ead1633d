package org.springblade.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.common.cache.DictBizCache;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(BladeSpringExtension.class)
@BladeBootTest(appName = "blade-runner", enableLoader = true)
public class Test14Redis {




	/*
	 */
	@Test
	public void start1() throws Exception {
		String name = "天津";
		String auditUser = DictBizCache.getKey("contract_signer", "质检工程师-"+name);
		System.out.println("auditUser:"+auditUser);

	}

}
