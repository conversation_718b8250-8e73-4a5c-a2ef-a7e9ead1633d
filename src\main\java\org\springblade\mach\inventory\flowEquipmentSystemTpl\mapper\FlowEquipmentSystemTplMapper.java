/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowEquipmentSystemTpl.mapper;

import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.entity.FlowEquipmentSystemTplEntity;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.vo.FlowEquipmentSystemTplVO;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.excel.FlowEquipmentSystemTplExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备系统标准表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
public interface FlowEquipmentSystemTplMapper extends BaseMapper<FlowEquipmentSystemTplEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param flowEquipmentSystemTpl
	 * @return
	 */
	List<FlowEquipmentSystemTplVO> selectFlowEquipmentSystemTplPage(@Param("page") IPage page, @Param("flowEquipmentSystemTpl") FlowEquipmentSystemTplVO flowEquipmentSystemTpl);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<FlowEquipmentSystemTplExcel> exportFlowEquipmentSystemTpl(@Param("ew") Wrapper<FlowEquipmentSystemTplEntity> queryWrapper);

	/**
	 * 获取顶级设备类型关联的设备系统
	 * @param topLevelId 设备类型id
	 * @return
	 */
    List<FlowEquipmentSystemTplVO> getEquipmentSystemList(Long topLevelId);
}
