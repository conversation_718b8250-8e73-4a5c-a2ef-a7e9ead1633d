/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowMaterialInfo.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NullSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 物料信息 实体类
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("flow_material_info")
@Schema(description = "FlowMaterialInfo对象")
@EqualsAndHashCode(callSuper = true)
public class FlowMaterialInfoEntity extends BaseEntity {

	/**
	 * 物料编码
	 */
	@Schema(description = "物料编码")
	private String materialCode;
	/**
	 * 物料名称
	 */
	@Schema(description = "物料名称")
	private String materialName;
	/**
	 * 规格型号
	 */
	@Schema(description = "规格型号")
	private String specModel;
	/**
	 * 单位
	 */
	@Schema(description = "单位")
	private String unit;
	/**
	 * 品牌
	 */
	@Schema(description = "品牌")
	private String brand;
	/**
	 * 单价元
	 */
	@Schema(description = "单价元")
	private String unitPrice;
	/**
	 * 计价方式 1：按件单价 2：材质单价
	 */
	@Schema(description = "计价方式")
	private Integer pricingMode;
	/**
	 * 型号说明
	 */
	@Schema(description = "型号说明")
	private String modelDescription;
	/**
	 * 物料类别
	 */
	@Schema(description = "物料类别")
	private String materialCategory;
	/**
	 * 物料子类别
	 */
	@Schema(description = "物料子类别")
	private String materialSubcategory;
	/**
	 * 配件应用
	 */
	@Schema(description = "配件应用")
	private String materialApplication;
	/**
	 * 其它描述
	 */
	@Schema(description = "其它描述")
	private String otherDescription;
	/**
	 * 标准
	 */
	@Schema(description = "标准")
	private String materialStandard;
	/**
	 * 材质
	 */
	@Schema(description = "材质")
	private String materialMaterial;
	/**
	 * 存储位置/货位
	 */
	@Schema(description = "存储位置/货位")
	private String storageLocation;
	/**
	 * 最低存储温度（℃）
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "最低存储温度（℃）")
	private BigDecimal minTemp;
	/**
	 * 最高存储温度（℃）
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "最高存储温度（℃）")
	private BigDecimal maxTemp;
	/**
	 * 最低存储湿度（%）
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "最低存储湿度（%）")
	private BigDecimal minHumidity;
	/**
	 * 最高存储湿度（%）
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "最高存储湿度（%）")
	private BigDecimal maxHumidity;
	/**
	 * 保质期（月）
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "保质期（月）")
	private Integer shelfLife;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;

}
