package org.springblade.mach.basis.supplier.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 供应商 实体类
 *
 * <AUTHOR>
 * @since 2024-05-12
 */
@Data
@TableName("flow_supplier")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商实体")
public class SupplierEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 供应商名称
	 */
	@Schema(description = "供应商名称")
	private String name;

	/**
	 * 供应商简称
	 */
	@Schema(description = "供应商简称")
	private String shortName;

	/**
	 * 开户行
	 */
	@Schema(description = "开户行")
	private String bankName;

	/**
	 * 银行账号
	 */
	@Schema(description = "银行账号")
	private String bankAccount;

	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	private String contactPerson;

	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String contactPhone;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;
}
