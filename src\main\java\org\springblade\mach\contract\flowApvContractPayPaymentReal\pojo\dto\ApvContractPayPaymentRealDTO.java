/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.dto;

import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 付款单与款单关联表 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApvContractPayPaymentRealDTO extends ApvContractPayPaymentRealEntity {
	@Serial
	private static final long serialVersionUID = 1L;

}
