/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowTechSpecDic.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.inventory.flowTechSpecDic.pojo.entity.FlowTechSpecDicEntity;
import org.springblade.mach.inventory.flowTechSpecDic.pojo.vo.FlowTechSpecDicVO;
import org.springblade.mach.inventory.flowTechSpecDic.excel.FlowTechSpecDicExcel;
import org.springblade.mach.inventory.flowTechSpecDic.wrapper.FlowTechSpecDicWrapper;
import org.springblade.mach.inventory.flowTechSpecDic.service.IFlowTechSpecDicService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 技术参数字典 控制器
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-flowTechSpecDic/flowTechSpecDic")
@Tag(name = "技术参数字典", description = "技术参数字典接口")
public class FlowTechSpecDicController extends BladeController {

	private final IFlowTechSpecDicService flowTechSpecDicService;

	/**
	 * 技术参数字典 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入flowTechSpecDic")
	public R<FlowTechSpecDicVO> detail(FlowTechSpecDicEntity flowTechSpecDic) {
		FlowTechSpecDicEntity detail = flowTechSpecDicService.getOne(Condition.getQueryWrapper(flowTechSpecDic));
		return R.data(FlowTechSpecDicWrapper.build().entityVO(detail));
	}
	/**
	 * 技术参数字典 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入flowTechSpecDic")
	public R<IPage<FlowTechSpecDicVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> flowTechSpecDic, Query query) {
		IPage<FlowTechSpecDicEntity> pages = flowTechSpecDicService.page(Condition.getPage(query), Condition.getQueryWrapper(flowTechSpecDic, FlowTechSpecDicEntity.class));
		return R.data(FlowTechSpecDicWrapper.build().pageVO(pages));
	}

	/**
	 * 技术参数字典 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入flowTechSpecDic")
	public R<IPage<FlowTechSpecDicVO>> page(FlowTechSpecDicVO flowTechSpecDic, Query query) {
		IPage<FlowTechSpecDicVO> pages = flowTechSpecDicService.selectFlowTechSpecDicPage(Condition.getPage(query), flowTechSpecDic);
		return R.data(pages);
	}

	/**
	 * 技术参数字典 自定义分页
	 */
	@GetMapping("/getTechSpecListBySystemId")
	public R<List<FlowTechSpecDicEntity>> getTechSpecListBySystemId(@RequestParam("systemId") Long systemId) {
		List<FlowTechSpecDicEntity> list = flowTechSpecDicService.getTechSpecListBySystemId(systemId);
		return R.data(list);
	}

	/**
	 * 技术参数字典 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入flowTechSpecDic")
	public R save(@Valid @RequestBody FlowTechSpecDicEntity flowTechSpecDic) {
		return R.status(flowTechSpecDicService.save(flowTechSpecDic));
	}

	/**
	 * 技术参数字典 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入flowTechSpecDic")
	public R update(@Valid @RequestBody FlowTechSpecDicEntity flowTechSpecDic) {
		return R.status(flowTechSpecDicService.updateById(flowTechSpecDic));
	}

	/**
	 * 技术参数字典 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入flowTechSpecDic")
	public R submit(@Valid @RequestBody FlowTechSpecDicEntity flowTechSpecDic) {
		return R.status(flowTechSpecDicService.saveOrUpdate(flowTechSpecDic));
	}

	/**
	 * 技术参数字典 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(flowTechSpecDicService.removeByIds(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-flowTechSpecDic")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入flowTechSpecDic")
	public void exportFlowTechSpecDic(@Parameter(hidden = true) @RequestParam Map<String, Object> flowTechSpecDic, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FlowTechSpecDicEntity> queryWrapper = Condition.getQueryWrapper(flowTechSpecDic, FlowTechSpecDicEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(FlowTechSpecDic::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(FlowTechSpecDicEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FlowTechSpecDicExcel> list = flowTechSpecDicService.exportFlowTechSpecDic(queryWrapper);
		ExcelUtil.export(response, "技术参数字典数据" + DateUtil.time(), "技术参数字典数据表", list, FlowTechSpecDicExcel.class);
	}


	/**
	 * 获取最大的行号
	 */
	@GetMapping("/getMaxRow")
	public R<Integer> getMaxRow(Long systemId) {
		Integer maxRow = flowTechSpecDicService.getMaxRow(systemId);
		return R.data(maxRow,"获取成功");
	}
}
