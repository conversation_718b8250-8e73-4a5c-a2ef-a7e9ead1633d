/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;
import org.springblade.mach.contract.flowApvContractPayDetail.service.IApvContractPayDetailService;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.service.IApvContractPayPaymentRealService;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;
import java.util.Objects;

/**
 * 付款审批 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public class ApvContractPayInfoWrapper extends BaseEntityWrapper<ApvContractPayInfoEntity, ApvContractPayInfoVO>  {

	public static ApvContractPayInfoWrapper build() {
		return new ApvContractPayInfoWrapper();
 	}

	@Override
	public ApvContractPayInfoVO entityVO(ApvContractPayInfoEntity apvContractPayInfo) {
		ApvContractPayInfoVO apvContractPayInfoVO = Objects.requireNonNull(BeanUtil.copyProperties(apvContractPayInfo, ApvContractPayInfoVO.class));

		// 获取Service实例
		IApvContractPayDetailService payDetailService = SpringUtil.getBean(IApvContractPayDetailService.class);
		IApvContractPayPaymentRealService paymentRealService = SpringUtil.getBean(IApvContractPayPaymentRealService.class);

		// 加载支付方式列表
		apvContractPayInfoVO.setPayDetailList(
			payDetailService.lambdaQuery()
				.eq(ApvContractPayDetailEntity::getPayInfoId, apvContractPayInfo.getId())
				.list()
		);

		// 加载请款单列表
		apvContractPayInfoVO.setPaymentRealList(
			paymentRealService.lambdaQuery()
				.eq(ApvContractPayPaymentRealEntity::getPayInfoId, apvContractPayInfo.getId())
				.list()
		);

		//User createUser = UserCache.getUser(apvContractPayInfo.getCreateUser());
		//User updateUser = UserCache.getUser(apvContractPayInfo.getUpdateUser());
		//apvContractPayInfoVO.setCreateUserName(createUser.getName());
		//apvContractPayInfoVO.setUpdateUserName(updateUser.getName());

		return apvContractPayInfoVO;
	}


}