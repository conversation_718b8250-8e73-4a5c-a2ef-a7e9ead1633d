/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmTechSpec.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.mach.crm.crmTechSpec.pojo.entity.CrmTechSpecEntity;
import org.springblade.mach.crm.crmTechSpec.pojo.vo.CrmTechSpecVO;
import org.springblade.mach.crm.crmTechSpec.excel.CrmTechSpecExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 技术参数 服务类
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface ICrmTechSpecService extends IService<CrmTechSpecEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param crmTechSpec
	 * @return
	 */
	IPage<CrmTechSpecVO> selectCrmTechSpecPage(IPage<CrmTechSpecVO> page, CrmTechSpecVO crmTechSpec);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<CrmTechSpecExcel> exportCrmTechSpec(Wrapper<CrmTechSpecEntity> queryWrapper);

	/**
	 * 根据projectInfoId获取技术参数
	 * @param projectInfoId
	 * @return
	 */
    List<CrmTechSpecEntity> getCrmTechSpecByProjectInfoId(Long projectInfoId);
}
