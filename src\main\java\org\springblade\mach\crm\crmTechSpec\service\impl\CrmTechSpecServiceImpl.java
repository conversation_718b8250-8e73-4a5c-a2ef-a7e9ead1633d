/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmTechSpec.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springblade.common.constant.DsKey;
import org.springblade.mach.crm.crmTechSpec.pojo.entity.CrmTechSpecEntity;
import org.springblade.mach.crm.crmTechSpec.pojo.vo.CrmTechSpecVO;
import org.springblade.mach.crm.crmTechSpec.excel.CrmTechSpecExcel;
import org.springblade.mach.crm.crmTechSpec.mapper.CrmTechSpecMapper;
import org.springblade.mach.crm.crmTechSpec.service.ICrmTechSpecService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**
 * 技术参数 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@DS(DsKey.CRM)
@Service
public class CrmTechSpecServiceImpl extends ServiceImpl<CrmTechSpecMapper, CrmTechSpecEntity> implements ICrmTechSpecService {

	@Override
	public IPage<CrmTechSpecVO> selectCrmTechSpecPage(IPage<CrmTechSpecVO> page, CrmTechSpecVO crmTechSpec) {
		return page.setRecords(baseMapper.selectCrmTechSpecPage(page, crmTechSpec));
	}


	@Override
	public List<CrmTechSpecExcel> exportCrmTechSpec(Wrapper<CrmTechSpecEntity> queryWrapper) {
		List<CrmTechSpecExcel> crmTechSpecList = baseMapper.exportCrmTechSpec(queryWrapper);
		//crmTechSpecList.forEach(crmTechSpec -> {
		//	crmTechSpec.setTypeName(DictCache.getValue(DictEnum.YES_NO, CrmTechSpec.getType()));
		//});
		return crmTechSpecList;
	}

	@Override
	public List<CrmTechSpecEntity> getCrmTechSpecByProjectInfoId(Long projectInfoId) {
		return this.lambdaQuery().eq(CrmTechSpecEntity::getProjectInfoId, projectInfoId).list();
	}

}
