/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springblade.core.tool.api.R;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;
import org.springblade.mach.contract.flowApvContractPayInfo.excel.ApvContractPayInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.io.OutputStream;
import java.util.List;

/**
 * 付款审批 服务类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface IApvContractPayInfoService extends BaseService<ApvContractPayInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param apvContractPayInfo
	 * @return
	 */
	IPage<ApvContractPayInfoVO> selectApvContractPayInfoPage(IPage<ApvContractPayInfoVO> page, ApvContractPayInfoVO apvContractPayInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<ApvContractPayInfoExcel> exportApvContractPayInfo(Wrapper<ApvContractPayInfoEntity> queryWrapper);

	/**
	 * 查看付款审批PDF
	 *
	 * @param id 付款审批ID
	 * @param request HTTP请求
	 * @param response HTTP响应
	 */
	void viewApvPdfOutStream(Long id, HttpServletRequest request, HttpServletResponse response);

	/**
	 * 查看付款审批附件
	 *
	 * @param id 付款审批ID
	 * @param request HTTP请求
	 * @param response HTTP响应
	 */
	void viewAttachmentsPdfOutStream(Long id, HttpServletRequest request, HttpServletResponse response);


	/**
	 * 提交付款审批
	 *
	 * @param apvContractPayInfo 付款审批信息
	 */
	void submitByDef(@Valid ApvContractPayInfoVO apvContractPayInfo);
}
