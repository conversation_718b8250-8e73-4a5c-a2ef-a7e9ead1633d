/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayDetail.service.IApvContractPayDetailService;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;
import org.springblade.mach.contract.flowApvContractPayInfo.excel.ApvContractPayInfoExcel;
import org.springblade.mach.contract.flowApvContractPayInfo.wrapper.ApvContractPayInfoWrapper;
import org.springblade.mach.contract.flowApvContractPayInfo.service.IApvContractPayInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 付款审批 控制器
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-apvContractPayInfo/apvContractPayInfo")
@Tag(name = "付款审批", description = "付款审批接口")
public class ApvContractPayInfoController extends BladeController {

	private final IApvContractPayInfoService apvContractPayInfoService;


	/**
	 * 付款审批 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入apvContractPayInfo")
	public R<ApvContractPayInfoVO> detail(ApvContractPayInfoEntity apvContractPayInfo) {
		ApvContractPayInfoEntity detail = apvContractPayInfoService.getOne(Condition.getQueryWrapper(apvContractPayInfo));
		return R.data(ApvContractPayInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 付款审批 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入apvContractPayInfo")
	public R<IPage<ApvContractPayInfoVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayInfo, Query query) {
		IPage<ApvContractPayInfoEntity> pages = apvContractPayInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(apvContractPayInfo, ApvContractPayInfoEntity.class));
		return R.data(ApvContractPayInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 付款审批 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入apvContractPayInfo")
	public R<IPage<ApvContractPayInfoVO>> page(ApvContractPayInfoVO apvContractPayInfo, Query query) {
		IPage<ApvContractPayInfoVO> pages = apvContractPayInfoService.selectApvContractPayInfoPage(Condition.getPage(query), apvContractPayInfo);
		return R.data(pages);
	}

	/**
	 * 付款审批 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入apvContractPayInfo")
	public R save(@Valid @RequestBody ApvContractPayInfoEntity apvContractPayInfo) {
		return R.status(apvContractPayInfoService.save(apvContractPayInfo));
	}

	/**
	 * 付款审批 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入apvContractPayInfo")
	public R update(@Valid @RequestBody ApvContractPayInfoEntity apvContractPayInfo) {
		return R.status(apvContractPayInfoService.updateById(apvContractPayInfo));
	}

	/**
	 * 付款审批 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入apvContractPayInfo")
	public R<?> submit(@Valid @RequestBody ApvContractPayInfoVO apvContractPayInfo) {
		apvContractPayInfoService.submitByDef(apvContractPayInfo);
		return R.data(apvContractPayInfo.getId());
	}

	/**
	 * 付款审批 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(apvContractPayInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 查看付款审批PDF
	 */
	@GetMapping("/viewApvPdfOutStream")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "查看付款审批PDF", description = "传入id")
	public void viewApvPdfOutStream(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response) {
		apvContractPayInfoService.viewApvPdfOutStream(id, request, response);
	}

	/**
	 * 查看付款审批附件
	 */
	@GetMapping("/viewAttachmentsPdfOutStream")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "查看付款审批附件", description = "传入id")
	public void viewAttachmentsPdfOutStream(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response) {
		apvContractPayInfoService.viewAttachmentsPdfOutStream(id, request, response);
	}

	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-apvContractPayInfo")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入apvContractPayInfo")
	public void exportApvContractPayInfo(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ApvContractPayInfoEntity> queryWrapper = Condition.getQueryWrapper(apvContractPayInfo, ApvContractPayInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ApvContractPayInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ApvContractPayInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ApvContractPayInfoExcel> list = apvContractPayInfoService.exportApvContractPayInfo(queryWrapper);
		ExcelUtil.export(response, "付款审批数据" + DateUtil.time(), "付款审批数据表", list, ApvContractPayInfoExcel.class);
	}

}
