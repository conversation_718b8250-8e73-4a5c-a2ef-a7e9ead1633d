<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.crm.crmTechSpec.mapper.CrmTechSpecMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="crmTechSpecResultMap" type="org.springblade.mach.crm.crmTechSpec.pojo.entity.CrmTechSpecEntity">
        <result column="id" property="id"/>
        <result column="project_info_id" property="projectInfoId"/>
        <result column="code" property="code"/>
        <result column="val" property="val"/>
    </resultMap>


    <select id="selectCrmTechSpecPage" resultMap="crmTechSpecResultMap">
        select * from crm_tech_spec where is_deleted = 0
    </select>


    <select id="exportCrmTechSpec" resultType="org.springblade.mach.crm.crmTechSpec.excel.CrmTechSpecExcel">
        SELECT * FROM crm_tech_spec ${ew.customSqlSegment}
    </select>

</mapper>
