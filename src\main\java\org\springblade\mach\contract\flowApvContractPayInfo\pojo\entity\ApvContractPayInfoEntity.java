/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.support.Kv;
import org.springblade.flow.core.entity.FlowEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 付款审批 实体类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@TableName("flow_apv_contract_pay_info")
@Schema(description = "ApvContractPayInfo对象")
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApvContractPayInfoEntity extends FlowEntity {

	/**
	 * 审批状态
	 */
	public static final String[] STATUS = {"审批中", "审批完成", "已驳回"};

	/**
	 * 任务节点映射
	 */
	public static final Kv taskKeys = Kv.init()
		.set("contract_resp",1)	//合同请款
		.set("supply_chain_center",2)	//供应链中心
		.set("senior",3)	//分管高管
		.set("ev",31)//常务副总
		.set("fpd",4);	//财务经理审批




	/**
	 * 流程ID
	 */
	@Schema(description = "流程ID")
	private String procInsId;
	/**
	 * 编号
	 */
	@Schema(description = "编号")
	private String code;
	/**
	 * 甲方
	 */
	@Schema(description = "甲方")
	private String partyA;
	/**
	 * 乙方
	 */
	@Schema(description = "乙方")
	private String partyB;
	/**
	 * 收款单位
	 */
	@Schema(description = "收款单位")
	private String payee;
	/**
	 * 单位简称
	 */
	@Schema(description = "单位简称")
	private String payeeSimple;
	/**
	 * 紧急程度
	 */
	@Schema(description = "紧急程度")
	private String level;
	/**
	 * 款项用途
	 */
	@Schema(description = "款项用途")
	private String fundsUse;
	/**
	 * 支出说明
	 */
	@Schema(description = "支出说明")
	private String payDesc;
	/**
	 * 付款金额
	 */
	@Schema(description = "付款金额")
	private BigDecimal amount;
	/**
	 * 银行信息是否变更
	 */
	@Schema(description = "银行信息是否变更")
	private String bankChange;
	/**
	 * 开户行
	 */
	@Schema(description = "开户行")
	private String bankAddress;
	/**
	 * 账号
	 */
	@Schema(description = "账号")
	private String bankAccount;
	/**
	 * 是否开具发票
	 */
	@Schema(description = "是否开具发票")
	private String invoiceStatus;
	/**
	 * 发票备注
	 */
	@Schema(description = "发票备注")
	private String invoiceRemark;
	/**
	 * 货物是否入库
	 */
	@Schema(description = "货物是否入库")
	private String instoreStatus;
	/**
	 * 入库备注
	 */
	@Schema(description = "入库备注")
	private String instoreRemark;
	/**
	 * 支付方式说明
	 */
	@Schema(description = "支付方式说明")
	private String paytypeDesc;
	/**
	 * 申请人
	 */
	@Schema(description = "申请人")
	private String applicantName;
	/**
	 * 审批人
	 */
	@Schema(description = "审批人")
	private String approverName;
	/**
	 * 付款人
	 */
	@Schema(description = "付款人")
	private String payerName;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;
	/**
	 * 付款时间
	 */
	@Schema(description = "支付时间")
	private Date payTime;

	@Schema(description = "附件")
	private String attachments;

	/**
	 * 类型 1:采购合同 2：安装合同
	 */
	@Schema(description = "类型")
	private String type;



}
