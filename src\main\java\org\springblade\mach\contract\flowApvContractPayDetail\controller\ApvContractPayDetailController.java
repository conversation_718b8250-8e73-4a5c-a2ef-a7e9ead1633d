/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.contract.flowApvContractPayDetail.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.mach.contract.flowApvContractPayInfo.service.IApvContractPayInfoService;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.vo.ApvContractPayDetailVO;
import org.springblade.mach.contract.flowApvContractPayDetail.excel.ApvContractPayDetailExcel;
import org.springblade.mach.contract.flowApvContractPayDetail.wrapper.ApvContractPayDetailWrapper;
import org.springblade.mach.contract.flowApvContractPayDetail.service.IApvContractPayDetailService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 付款审批支付明细 控制器
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-apvContractPayDetail/apvContractPayDetail")
@Tag(name = "付款审批支付明细", description = "付款审批支付明细接口")
public class ApvContractPayDetailController extends BladeController {

	private final IApvContractPayDetailService apvContractPayDetailService;

	private final IApvContractPayInfoService apvContractPayInfoService;

	/**
	 * 付款审批支付明细 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入apvContractPayDetail")
	public R<ApvContractPayDetailVO> detail(ApvContractPayDetailEntity apvContractPayDetail) {
		ApvContractPayDetailEntity detail = apvContractPayDetailService.getOne(Condition.getQueryWrapper(apvContractPayDetail));
		return R.data(ApvContractPayDetailWrapper.build().entityVO(detail));
	}
	/**
	 * 付款审批支付明细 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入apvContractPayDetail")
	public R<IPage<ApvContractPayDetailVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayDetail, Query query) {
		IPage<ApvContractPayDetailEntity> pages = apvContractPayDetailService.page(Condition.getPage(query), Condition.getQueryWrapper(apvContractPayDetail, ApvContractPayDetailEntity.class));
		return R.data(ApvContractPayDetailWrapper.build().pageVO(pages));
	}

	/**
	 * 付款审批支付明细 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入apvContractPayDetail")
	public R<IPage<ApvContractPayDetailVO>> page(ApvContractPayDetailVO apvContractPayDetail, Query query) {
		IPage<ApvContractPayDetailVO> pages = apvContractPayDetailService.selectApvContractPayDetailPage(Condition.getPage(query), apvContractPayDetail);
		return R.data(pages);
	}

	/**
	 * 付款审批支付明细 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入apvContractPayDetail")
	public R save(@Valid @RequestBody ApvContractPayDetailEntity apvContractPayDetail) {
		return R.status(apvContractPayDetailService.save(apvContractPayDetail));
	}

	/**
	 * 付款审批支付明细 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入apvContractPayDetail")
	public R update(@Valid @RequestBody ApvContractPayDetailEntity apvContractPayDetail) {
		return R.status(apvContractPayDetailService.updateById(apvContractPayDetail));
	}

	/**
	 * 付款审批支付明细 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入apvContractPayDetail")
	public R submit(@Valid @RequestBody ApvContractPayDetailEntity apvContractPayDetail) {
		return R.status(apvContractPayDetailService.saveOrUpdate(apvContractPayDetail));
	}



	/**
	 * 付款审批支付明细 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(apvContractPayDetailService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-apvContractPayDetail")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入apvContractPayDetail")
	public void exportApvContractPayDetail(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayDetail, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ApvContractPayDetailEntity> queryWrapper = Condition.getQueryWrapper(apvContractPayDetail, ApvContractPayDetailEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ApvContractPayDetail::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ApvContractPayDetailEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ApvContractPayDetailExcel> list = apvContractPayDetailService.exportApvContractPayDetail(queryWrapper);
		ExcelUtil.export(response, "付款审批支付明细数据" + DateUtil.time(), "付款审批支付明细数据表", list, ApvContractPayDetailExcel.class);
	}

}
