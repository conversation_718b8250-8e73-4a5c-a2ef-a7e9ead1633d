/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowEquipmentSystemTpl.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.entity.FlowEquipmentSystemTplEntity;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.vo.FlowEquipmentSystemTplVO;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.excel.FlowEquipmentSystemTplExcel;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.wrapper.FlowEquipmentSystemTplWrapper;
import org.springblade.mach.inventory.flowEquipmentSystemTpl.service.IFlowEquipmentSystemTplService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 设备系统标准表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-flowEquipmentSystemTpl/flowEquipmentSystemTpl")
@Tag(name = "设备系统标准表", description = "设备系统标准表接口")
public class FlowEquipmentSystemTplController extends BladeController {

	private final IFlowEquipmentSystemTplService flowEquipmentSystemTplService;

	/**
	 * 设备系统标准表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入flowEquipmentSystemTpl")
	public R<FlowEquipmentSystemTplVO> detail(FlowEquipmentSystemTplEntity flowEquipmentSystemTpl) {
		FlowEquipmentSystemTplEntity detail = flowEquipmentSystemTplService.getOne(Condition.getQueryWrapper(flowEquipmentSystemTpl));
		return R.data(FlowEquipmentSystemTplWrapper.build().entityVO(detail));
	}
	/**
	 * 设备系统标准表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入flowEquipmentSystemTpl")
	public R<IPage<FlowEquipmentSystemTplVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> flowEquipmentSystemTpl, Query query) {
		IPage<FlowEquipmentSystemTplEntity> pages = flowEquipmentSystemTplService.page(Condition.getPage(query), Condition.getQueryWrapper(flowEquipmentSystemTpl, FlowEquipmentSystemTplEntity.class));
		return R.data(FlowEquipmentSystemTplWrapper.build().pageVO(pages));
	}

	/**
	 * 设备系统标准表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入flowEquipmentSystemTpl")
	public R<IPage<FlowEquipmentSystemTplVO>> page(FlowEquipmentSystemTplVO flowEquipmentSystemTpl, Query query) {
		IPage<FlowEquipmentSystemTplVO> pages = flowEquipmentSystemTplService.selectFlowEquipmentSystemTplPage(Condition.getPage(query), flowEquipmentSystemTpl);
		return R.data(pages);
	}

	/**
	 * 设备系统标准表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入flowEquipmentSystemTpl")
	public R save(@Valid @RequestBody FlowEquipmentSystemTplEntity flowEquipmentSystemTpl) {
		return R.status(flowEquipmentSystemTplService.save(flowEquipmentSystemTpl));
	}

	/**
	 * 设备系统标准表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入flowEquipmentSystemTpl")
	public R update(@Valid @RequestBody FlowEquipmentSystemTplEntity flowEquipmentSystemTpl) {
		return R.status(flowEquipmentSystemTplService.updateById(flowEquipmentSystemTpl));
	}

	/**
	 * 设备系统标准表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入flowEquipmentSystemTpl")
	public R submit(@Valid @RequestBody FlowEquipmentSystemTplEntity flowEquipmentSystemTpl) {
		return R.status(flowEquipmentSystemTplService.saveOrUpdate(flowEquipmentSystemTpl));
	}

	/**
	 * 设备系统标准表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(flowEquipmentSystemTplService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-flowEquipmentSystemTpl")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入flowEquipmentSystemTpl")
	public void exportFlowEquipmentSystemTpl(@Parameter(hidden = true) @RequestParam Map<String, Object> flowEquipmentSystemTpl, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FlowEquipmentSystemTplEntity> queryWrapper = Condition.getQueryWrapper(flowEquipmentSystemTpl, FlowEquipmentSystemTplEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(FlowEquipmentSystemTpl::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(FlowEquipmentSystemTplEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FlowEquipmentSystemTplExcel> list = flowEquipmentSystemTplService.exportFlowEquipmentSystemTpl(queryWrapper);
		ExcelUtil.export(response, "设备系统标准表数据" + DateUtil.time(), "设备系统标准表数据表", list, FlowEquipmentSystemTplExcel.class);
	}

	/**
	 * 获取顶级设备类型关联的设备系统
	 */
	@GetMapping("/getEquipmentSystemList")
	public R<List<FlowEquipmentSystemTplVO>> getEquipmentSystemList(@RequestParam Long topLevelId) {
		return R.data(flowEquipmentSystemTplService.getEquipmentSystemList(topLevelId));
	}

}
