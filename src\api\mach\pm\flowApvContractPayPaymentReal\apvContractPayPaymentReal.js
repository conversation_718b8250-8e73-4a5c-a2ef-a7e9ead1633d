import request from '@/axios';

export const importExcel = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/blade-apvContractPayPaymentReal/apvContractPayPaymentReal/import-excel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const getList = (params) => {
  return request({
    url: '/blade-apvContractPayPaymentReal/apvContractPayPaymentReal/list',
    method: 'get',
    params
  })
}

export const save = (data) => {
  return request({
    url: '/blade-apvContractPayPaymentReal/apvContractPayPaymentReal/submit',
    method: 'post',
    data
  })
}

export const updatePaymentReal = (data) => {
  return request({
    url: '/blade-apvContractPayPaymentReal/apvContractPayPaymentReal/submit',
    method: 'post',
    data
  })
}

export const removePaymentReal = (params) => {
  return request({
    url: '/blade-apvContractPayPaymentReal/apvContractPayPaymentReal/remove',
    method: 'post',
    params
  })
}