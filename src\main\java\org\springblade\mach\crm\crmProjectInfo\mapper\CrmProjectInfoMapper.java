/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmProjectInfo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.constant.DsKey;
import org.springblade.mach.crm.crmProjectInfo.excel.CrmProjectInfoExcel;
import org.springblade.mach.crm.crmProjectInfo.pojo.entity.CrmProjectInfoEntity;
import org.springblade.mach.crm.crmProjectInfo.pojo.vo.CrmProjectInfoVO;

import java.util.List;

/**
 * 项目信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface CrmProjectInfoMapper extends BaseMapper<CrmProjectInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param crmProjectInfo
	 * @return
	 */
	List<CrmProjectInfoVO> selectCrmProjectInfoPage(IPage page, CrmProjectInfoVO crmProjectInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<CrmProjectInfoExcel> exportCrmProjectInfo(@Param("ew") Wrapper<CrmProjectInfoEntity> queryWrapper);

	/**
	 * 获取当年当月最大编号
	 * @return
	 */
	Integer getMaxCode();
}
