/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo;

import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;

import java.io.Serial;
import java.util.List;

/**
 * 付款审批 视图实体类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApvContractPayInfoVO extends ApvContractPayInfoEntity {
	@Serial
	private static final long serialVersionUID = 1L;

	private String totalAmountInWords;	//金额大写

	private String backTaskKey;	//驳回-节点key

	private List<ApvContractPayDetailEntity> payDetailList; // 支付方式列表

	private List<ApvContractPayPaymentRealEntity> paymentRealList; // 付款单列表
}
