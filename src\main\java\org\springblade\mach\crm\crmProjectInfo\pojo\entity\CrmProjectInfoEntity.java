/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.crm.crmProjectInfo.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NullSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 项目信息 实体类
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@TableName("crm_project_info")
@Schema(description = "CrmProjectInfo对象")
@EqualsAndHashCode(callSuper = true)
public class CrmProjectInfoEntity extends BaseEntity {

	/**
	 * 流程ID
	 */
	@Schema(description = "流程ID")
	private String procInsId;
	/**
	 * 编号
	 */
	@Schema(description = "编号")
	private String code;
	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private String customerName;
	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	private String contactPerson;
	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String contactPhone;
	/**
	 * 联系地址
	 */
	@Schema(description = "联系地址")
	private String contactAddress;
	/**
	 * 联系邮件
	 */
	@Schema(description = "联系邮件")
	private String contactEmail;
	/**
	 * 商务代表
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "商务代表")
	private Long saleId;
	/**
	 * 商务代表姓名
	 */
	@Schema(description = "商务代表姓名")
	private String saleName;
	/**
	 * 技术代表
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "技术代表")
	private Long techId;
	/**
	 * 技术代表姓名
	 */
	@Schema(description = "技术代表姓名")
	private String techName;
	/**
	 * 加工制造单位
	 */
	@Schema(description = "加工制造单位")
	private String factory;
	/**
	 * 报价日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Schema(description = "报价日期")
	private Date quotationDate;

	/**
	 * 设备类型定义
	 */
	@JsonSerialize(nullsUsing = NullSerializer.class)
	@Schema(description = "设备类型定义")
	private Long systemId;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;

}
