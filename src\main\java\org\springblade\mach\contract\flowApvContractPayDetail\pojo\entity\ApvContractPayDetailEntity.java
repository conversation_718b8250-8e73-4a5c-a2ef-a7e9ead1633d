/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 付款审批支付明细 实体类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@TableName("flow_apv_contract_pay_detail")
@Schema(description = "ApvContractPayDetail对象")
@EqualsAndHashCode(callSuper = true)
public class ApvContractPayDetailEntity extends BaseEntity {

	/**
	 * 付款审批ID
	 */
	@Schema(description = "付款审批ID")
	private Long payInfoId;
	/**
	 * 支付类型
	 */
	@Schema(description = "支付类型")
	private String type;
	/**
	 * 票号
	 */
	@Schema(description = "票号")
	private String ticketNo;
	/**
	 * 金额
	 */
	@Schema(description = "金额")
	private BigDecimal amount;
	/**
	 * 附件
	 */
	@Schema(description = "附件")
	private String files;
	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sorts;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;

}
