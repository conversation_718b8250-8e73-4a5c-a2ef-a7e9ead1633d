/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayDetail.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import jakarta.validation.Valid;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.vo.ApvContractPayDetailVO;
import org.springblade.mach.contract.flowApvContractPayDetail.excel.ApvContractPayDetailExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;

import java.util.List;

/**
 * 付款审批支付明细 服务类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface IApvContractPayDetailService extends BaseService<ApvContractPayDetailEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param apvContractPayDetail
	 * @return
	 */
	IPage<ApvContractPayDetailVO> selectApvContractPayDetailPage(IPage<ApvContractPayDetailVO> page, ApvContractPayDetailVO apvContractPayDetail);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<ApvContractPayDetailExcel> exportApvContractPayDetail(Wrapper<ApvContractPayDetailEntity> queryWrapper);

}
