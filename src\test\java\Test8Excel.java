import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.cell.CellUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springblade.common.utils.BigDecimalUtils;
import org.springblade.mach.contract.flowProcessingContractDetails.pojo.vo.FlowProcessingInfoVo;
import org.springblade.mach.contract.flowProcessingContractDetails.service.impl.FlowProcessingContractDetailsServiceImpl;
import org.springblade.mach.contract.flowProcessingContractDetails.service.impl.ReadProcessXlsListener;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class Test8Excel {

	public static void main(String[] args) throws Exception {

		InputStream inputStream = new FileInputStream("C:\\Users\\<USER>\\Downloads\\日照友和.xls");
		FlowProcessingInfoVo flowProcessingInfoVo = new FlowProcessingContractDetailsServiceImpl().analyzeProcessingXls(inputStream);
		System.out.println(flowProcessingInfoVo);

	}
}

