/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.flow.core.utils;

import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.constant.ProcessConstant;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流工具类
 *
 * <AUTHOR>
 */
public class FlowUtil {

//	/**
//	 * 定义流程key对应的表名
//	 */
//	private final static Map<String, String> BUSINESS_TABLE = new HashMap<>();
//
//	static {
//		BUSINESS_TABLE.put(ProcessConstant.LEAVE_KEY, "blade_process_leave");
//	}
//
//	/**
//	 * 通过流程key获取业务表名
//	 *
//	 * @param key 流程key
//	 */
//	public static String getBusinessTable(String key) {
//		String businessTable = BUSINESS_TABLE.get(key);
//		if (Func.isEmpty(businessTable)) {
//			throw new RuntimeException("流程启动失败,未找到相关业务表");
//		}
//		return businessTable;
//	}
	/**
	 * 请假流程（demo）
	 */
	public static final String[] LEAVE_KEY = new String[]{"Leave", "blade_process_leave"};

	/**
	 * 马赫经营计划流程主流程
	 */
	public static final String[] PD_MACH_AUDIT = new String[]{"mach_audit", "flow_project_eq_details"};

	/**
	 * 项目一览表审核流程
	 */
	public static final String[] PD_PROJECT_AUDIT = new String[]{"project_audit", "flow_project_schedule"};
	/**
	 * 标准组备注流程（单套产品）
	 */
	public static final String[] PD_STANDARD_AUDIT = new String[]{"standard_audit", "flow_project_eq_standard"};

	/**
	 * 项目一览表备件流程
	 */
	public static final String[] PD_SPARE_PART = new String[]{"spare_part", "flow_project_schedule"};

	/**
	 * 机械设计图纸流程
	 */
	public static final String[] PD_MACHINE_AUDIT = new String[]{"machine_audit", "flow_project_node"};

	/**
	 * 重新投产流程
	 */
	public static final String[] PD_PRODUCTION_AUDIT = new String[]{"production_audit", "flow_project_node"};

	/**
	 * 自控条件图流程
	 */
	public static final String[] PD_AUTO_AUDIT = new String[]{"auto_audit", "flow_project_node"};

	/**
	 * 机械物料需求清单
	 */
	public static final String[] PD_MM_AUDIT = new String[]{"MM_audit", "flow_project_node"};

	/**
	 * 电气设计图纸流程
	 */
	public static final String[] PD_ELECTRICAL_AUDIT = new String[]{"electrical_audit", "flow_project_node"};

	/**
	 * 电气物料需求清单流程
	 */
	public static final String[] PD_EM_AUDIT = new String[]{"EM_audit", "flow_project_node"};

	/**
	 * 机械采购清单流程
	 */
	public static final String[] PD_MP_AUDIT = new String[]{"MP_audit", "flow_project_node"};

	/**
	 * 电气采购清单流程
	 */
	public static final String[] PD_EP_AUDIT = new String[]{"EP_audit", "flow_project_node"};

	/**
	 * 软件采购清单流程
	 */
	public static final String[] PD_SOFTWAREP_AUDIT = new String[]{"SOFTWAREP_audit", "flow_project_node"};

	/**
	 * 软件设计资料流程
	 */
	public static final String[] PD_SP_AUDIT = new String[]{"SP_audit", "flow_project_node"};

	/**
	 * 电气控制程序流程
	 */
	public static final String[] PD_PLC_AUDIT = new String[]{"PLC_audit", "flow_project_node"};

	/**
	 * 软件开发测试流程
	 */
	public static final String[] PD_SOFTWARE_AUDIT = new String[]{"software_audit", "flow_project_node"};

	/**
	 * 电气物料需求清单流程
	 */
	public static final String[] PD_SM_AUDIT = new String[]{"SM_audit", "flow_project_node"};

	/**
	 * 计划发货流程
	 */
	public static final String[] PD_PLANDELIVER_AUDIT = new String[]{"planDeliver_audit", "flow_project_node"};

	/**
	 * 发货流程
	 */
	public static final String[] PD_DELIVER_AUDIT = new String[]{"deliver_audit", "flow_project_node"};

	/**
	 * 安装调试流程
	 */
	public static final String[] PD_INSTALL_AUDIT = new String[]{"install_audit", "flow_project_node"};

	/**
	 * 验收流程
	 */
	public static final String[] PD_CBA_AUDIT = new String[]{"cba_audit", "flow_project_node"};

	/**
	 * 质保流程
	 */
	public static final String[] PD_WARRANTY_AUDIT = new String[]{"warranty_audit", "flow_project_node"};

	/**
	 * 销售强制发货流程
	 */
	public static final String[] SALE_DELIVER = new String[]{"sale_deliver", "flow_sale_deliver"};

	/**
	 * 销售启动修改一览表流程
	 */
	public static final String[] PROJECT_APPLY = new String[]{"project_apply", "flow_project_apply"};

	/**
	 * 应急单流程
	 */
	public static final String[] EMERGENCY_AUDIT = new String[]{"emergency_audit", "flow_project_eq_details_emergency"};

	/**
	 * 到货款流程
	 */
	public static final String[] PD_PAYMENT_AUDIT = new String[]{"payment_audit","flow_project_pay"};

	/**
	 * 预算管理流程
	 */
	public static final String[] PD_BUDGET_AUDIT = new String[]{"budget_audit","flow_project_eq_budget_management"};

	/**
	 * 预算金额审核及维护流程
	 */
	public static final String[] PD_EQ_BUDGET_AUDIT = new String[]{"eq_budget_audit","flow_project_eq_budget_audit"};

	/**
	 * 安装过程质量把控流程
	 */
	public static final String[] PD_INSTALL_QUALITY_CONTROL = new String[]{"install_quality_control","flow_project_node"};

	/**
	 * 有效期设置
	 */
	public static final String[] PD_VALIDITY_AUDIT = new String[]{"validity_audit","flow_project_node"};

	/**
	 * 委托合同
	 */
	public static final String[] PD_ENTRUST_AUDIT = new String[]{"entrust_audit","flow_entrust_contract_details"};

	/**
	 * 作废合同
	 */
	public static final String[] PD_CANCEL_AUDIT = new String[]{"cancel_audit","flow_cancel_contract_details"};

	/**
	 * 设计合同请款流程
	 */
	public static final String[] PD_DESIGN_CONTRACT_AUDIT = new String[]{"design_contract_audit","flow_entrust_details_payment"};

	/**
	 * 大包合同请款流程
	 */
	public static final String[] PD_INCLUSIVE_AUDIT = new String[]{"inclusive_audit","flow_entrust_details_payment"};

	/**
	 * 作废合同请款流程
	 */
	public static final String[] PD_CANCEL_PAYMENT_AUDIT = new String[]{"cancel_payment_audit","flow_entrust_details_payment"};

	/**
	 * 加工合同签订流程
	 */
	public static final String[] PD_PROCESSING_CONTRACT_AUDIT = new String[]{"processing_contract_audit","flow_processing_contract_details"};
	/**
	 * 委外-加工合同请款流程
	 */
	public static final String[] PD_PROCESSING_CONTRACT_PAYMENT_AUDIT = new String[]{"processing_contract_payment_audit","flow_processing_contract_payment"};
	/**
	 * 委内-加工合同请款流程
	 */
	public static final String[] PD_PROCESSING_CONTRACT_PAYMENT_IN_AUDIT = new String[]{"processing_contract_payment_in_audit","flow_processing_contract_payment"};

	/**
	 * 加工合同作废流程
	 */
	public static final String[] PD_PROCESSING_CONTRACT_CANCEL_AUDIT = new String[]{"processing_contract_cancel_audit","flow_processing_contract_cancel_details"};
	/**
	 * 外协加工合同验收单
	 */
	public static final String[] PD_PROCESSING_CONTRACT_QC_AUDIT = new String[]{"processing_contract_qc_audit","flow_processing_contract_qc"};

	/**
	 * 电控柜合同签订流程
	 */
	public static final String[] PD_ELECTRICAL_CABINET_CONTRACT_AUDIT = new String[]{"electrical_cabinet_contract_audit","flow_electrical_cabinet_contract_details"};
	/**
	 * 电控柜合同请款流程
	 */
	public static final String[] PD_ELECTRICAL_CABINET_CONTRACT_PAYMENT_AUDIT = new String[]{"electrical_cabinet_contract_payment_audit","flow_electrical_cabinet_contract_payment"};
	/**
	 * 电控柜合同作废流程
	 */
	public static final String[] PD_ELECTRICAL_CABINET_CONTRACT_CANCEL_AUDIT = new String[]{"electrical_cabinet_contract_cancel_audit","flow_electrical_cabinet_contract_cancel"};


	/**
	 * 软件调试流程
	 */
	public static final String[] PD_SOFTWARE_DEBUGGER_AUDIT = new String[]{"software_debug","flow_project_node"};

	/**
	 * 项目成本核算-天津
	 */
	public static final String[] PD_COSTING_TIANJIN_AUDIT = new String[]{"cost_calc_flow","flow_project_node"};

	/**
	 * 项目成本核算-北京
	 */
	public static final String[] PD_COSTING_BEIJING_AUDIT = new String[]{"cost_calc_flow","flow_project_node"};
	/**
	 * 项目成本核算-江苏
	 */
	public static final String[] PD_COSTING_JIANGSU_AUDIT = new String[]{"cost_calc_flow","flow_project_node"};

	/**
	 * 合同签订比例变更
	 */
	public static final String[] PD_CONTRACT_RATIO_CHANGE_AUDIT = new String[]{"contract_ratio_change_audit","flow_contract_ratio_change"};

	/**
	 * 项目制造成本总预算
	 */
	public static final String[] PD_CONTRACT_BUDGET_SET_AUDIT = new String[]{"contract_budget_set_audit","flow_entrust_budget_set"};

	/**
	 * 单台设备预算
	 */
	public static final String[] PD_CONTRACT_BUDGET_PCS_AUDIT = new String[]{"contract_budget_pcs_audit","flow_entrust_budget_pcs"};

	/**
	 * 电控柜预算
	 */
	public static final String[] PD_CONTRACT_BUDGET_EC_AUDIT = new String[]{"contract_budget_ec_audit","flow_electrical_cabinet_budget_ec"};

	/**
	 * 付款申请-内部
	 */
	public static final String[] PD_CONTRACT_PAYINFO_IN_AUDIT = new String[]{"contract_payinfo_in_audit","flow_out_contract_pay_info"};

	/**
	 * 付款申请-外部
	 */
	public static final String[] PD_CONTRACT_PAYINFO_OUT_AUDIT = new String[]{"contract_payinfo_out_audit","flow_out_contract_pay_info"};

	/**
	 * 付款审批
	 */
	public static final String[] PD_CONTRACT_APV_AUDIT = new String[]{"apv_payinfo_audit","flow_apv_contract_pay_info"};

	/**
	 * 采购付款申请
	 */
	public static final String[] PD_MP_PAYMENT_AUDIT = new String[]{"mp_payment","flow_apv_contract_pay_info"};

	/**
	 * 变更任务承接人
	 */
	public static final String[] PD_CHANGE_TASK_USER_AUDIT = new String[]{"change_task_user_audit","flow_change_task_user"};

	/**
	 * 软件成本预算
	 */
	public static final String[] PD_SOFTWARE_BUDGET_AUDIT = new String[]{"software_budget_audit","flow_project_software_budget"};

	/**
	 * 获取业务标识
	 *
	 * @param businessTable 业务表
	 * @param businessId    业务表主键
	 * @return businessKey
	 */
	public static String getBusinessKey(String businessTable, String businessId) {
		return StringUtil.format("{}:{}", businessTable, businessId);
	}

}
