export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  menu: false,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  overHidden: true,
  addBtn: false, // 隐藏avue自带的新增按钮
  column: [
    {
      label: "主键ID",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "编号",
      prop: "code",
      type: "input",
      search: true,
      searchSpan: 6,
    },
    {
      label: "收款单位",
      prop: "payee",
      type: "input",
      search: true,
      searchSpan: 6,
    },
    {
      label: "单位简称",
      prop: "payeeSimple",
      type: "input",
    },
    {
      label: "紧急程度",
      prop: "level",
      type: "select",
      dicData: [
        {
          label: "一般",
          value: "1"
        },
        {
          label: "紧急",
          value: "2"
        },
        {
          label: "特急",
          value: "3"
        }
      ],
    },
    {
      label: "款项用途",
      prop: "fundsUse",
      type: "input",
    },
    {
      label: "付款金额",
      prop: "amount",
      type: "input",
    },
    {
      label: "开户行",
      prop: "bankAddress",
      type: "input",
    },
    {
      label: "账号",
      prop: "bankAccount",
      type: "input",
      hide: true,
    },
    {
      label: "是否开具发票",
      prop: "invoiceStatus",
      type: "select",
      dicUrl: "/blade-system/dict-biz/dictionary?code=yes_no",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
    },
    {
      label: "货物是否入库",
      prop: "instoreStatus",
      type: "select",
      dicUrl: "/blade-system/dict-biz/dictionary?code=yes_no",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
    },
    {
      label: "申请人",
      prop: "applicantName",
      type: "input",
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "备注信息",
      prop: "remarks",
      type: "input",
      hide: true,
    },
    // 隐藏不常用字段
    {
      label: "流程ID",
      prop: "procInsId",
      type: "input",
      hide: true,
    },
    {
      label: "甲方",
      prop: "partyA",
      type: "input",
      hide: true,
    },
    {
      label: "乙方",
      prop: "partyB",
      type: "input",
      hide: true,
    },
    {
      label: "支出说明",
      prop: "payDesc",
      type: "input",
      hide: true,
    },
    {
      label: "银行信息是否变更",
      prop: "bankChange",
      type: "input",
      hide: true,
    },
    {
      label: "发票备注",
      prop: "invoiceRemark",
      type: "input",
      hide: true,
    },
    {
      label: "入库备注",
      prop: "instoreRemark",
      type: "input",
      hide: true,
    },
    {
      label: "支付方式说明",
      prop: "paytypeDesc",
      type: "input",
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "审批人",
      prop: "approverName",
      type: "input",
      hide: true,
    },
    {
      label: "付款人",
      prop: "payerName",
      type: "input",
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "付款时间",
      prop: "payTime",
      type: "input",
      hide: true,
    },
  ]
}
