/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.contract.flowApvContractPayPaymentReal.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.vo.ApvContractPayPaymentRealVO;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.excel.ApvContractPayPaymentRealExcel;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.wrapper.ApvContractPayPaymentRealWrapper;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.service.IApvContractPayPaymentRealService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 付款单与款单关联表 控制器
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-apvContractPayPaymentReal/apvContractPayPaymentReal")
@Tag(name = "付款单与款单关联表", description = "付款单与款单关联表接口")
public class ApvContractPayPaymentRealController extends BladeController {

	private final IApvContractPayPaymentRealService apvContractPayPaymentRealService;

	/**
	 * 付款单与款单关联表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入apvContractPayPaymentReal")
	public R<ApvContractPayPaymentRealVO> detail(ApvContractPayPaymentRealEntity apvContractPayPaymentReal) {
		ApvContractPayPaymentRealEntity detail = apvContractPayPaymentRealService.getOne(Condition.getQueryWrapper(apvContractPayPaymentReal));
		return R.data(ApvContractPayPaymentRealWrapper.build().entityVO(detail));
	}
	/**
	 * 付款单与款单关联表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入apvContractPayPaymentReal")
	public R<IPage<ApvContractPayPaymentRealVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayPaymentReal, Query query) {
		IPage<ApvContractPayPaymentRealEntity> pages = apvContractPayPaymentRealService.page(Condition.getPage(query), Condition.getQueryWrapper(apvContractPayPaymentReal, ApvContractPayPaymentRealEntity.class));
		return R.data(ApvContractPayPaymentRealWrapper.build().pageVO(pages));
	}

	/**
	 * 付款单与款单关联表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入apvContractPayPaymentReal")
	public R<IPage<ApvContractPayPaymentRealVO>> page(ApvContractPayPaymentRealVO apvContractPayPaymentReal, Query query) {
		IPage<ApvContractPayPaymentRealVO> pages = apvContractPayPaymentRealService.selectApvContractPayPaymentRealPage(Condition.getPage(query), apvContractPayPaymentReal);
		return R.data(pages);
	}

	/**
	 * 付款单与款单关联表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入apvContractPayPaymentReal")
	public R save(@Valid @RequestBody ApvContractPayPaymentRealEntity apvContractPayPaymentReal) {
		return R.status(apvContractPayPaymentRealService.save(apvContractPayPaymentReal));
	}

	/**
	 * 付款单与款单关联表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入apvContractPayPaymentReal")
	public R update(@Valid @RequestBody ApvContractPayPaymentRealEntity apvContractPayPaymentReal) {
		return R.status(apvContractPayPaymentRealService.updateById(apvContractPayPaymentReal));
	}

	/**
	 * 付款单与款单关联表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入apvContractPayPaymentReal")
	public R submit(@Valid @RequestBody ApvContractPayPaymentRealEntity apvContractPayPaymentReal) {
		boolean success = apvContractPayPaymentRealService.saveOrUpdate(apvContractPayPaymentReal);
		if (success) {
			// 查询并返回保存后的数据
			ApvContractPayPaymentRealEntity savedData = apvContractPayPaymentRealService.getById(apvContractPayPaymentReal.getId());
			return R.data(savedData);
		}
		return R.fail("操作失败");
	}

	/**
	 * 付款单与款单关联表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(apvContractPayPaymentRealService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-apvContractPayPaymentReal")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入apvContractPayPaymentReal")
	public void exportApvContractPayPaymentReal(@Parameter(hidden = true) @RequestParam Map<String, Object> apvContractPayPaymentReal, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ApvContractPayPaymentRealEntity> queryWrapper = Condition.getQueryWrapper(apvContractPayPaymentReal, ApvContractPayPaymentRealEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ApvContractPayPaymentReal::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ApvContractPayPaymentRealEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ApvContractPayPaymentRealExcel> list = apvContractPayPaymentRealService.exportApvContractPayPaymentReal(queryWrapper);
		ExcelUtil.export(response, "付款单与款单关联表数据" + DateUtil.time(), "付款单与款单关联表数据表", list, ApvContractPayPaymentRealExcel.class);
	}

	/**
	 * Excel导入请款单数据
	 */
	@PostMapping("/import-excel")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "Excel导入", description = "传入Excel文件")
	public R<List<ApvContractPayPaymentRealEntity>> importExcel(@RequestParam("file") MultipartFile file) {
		List<ApvContractPayPaymentRealExcel> excelList = ExcelUtil.read(file, ApvContractPayPaymentRealExcel.class);
		if (excelList != null && !excelList.isEmpty()) {
			// 将Excel数据转换为实体类
			List<ApvContractPayPaymentRealEntity> entityList = excelList.stream()
				.map(excel -> {
					ApvContractPayPaymentRealEntity entity = new ApvContractPayPaymentRealEntity();
					entity.setPartyA(excel.getPartyA());
					entity.setPartyB(excel.getPartyB());
					entity.setContractCode(excel.getContractCode());
					entity.setEqAmount(excel.getEqAmount());
					entity.setEqPaymentAmount(excel.getEqPaymentAmount());
					entity.setRemarks(excel.getRemarks());
					return entity;
				})
				.toList();
			return R.data(entityList);
		}
		return R.fail("导入数据为空");
	}

	/**
	 * 新增单条请款单
	 */
	@PostMapping("/add")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "新增请款单", description = "传入请款单数据")
	public R<ApvContractPayPaymentRealEntity> addPaymentReal(@RequestBody ApvContractPayPaymentRealEntity paymentReal) {
		boolean success = apvContractPayPaymentRealService.save(paymentReal);
		if (success) {
			return R.data(paymentReal);
		}
		return R.fail("新增请款单失败");
	}

}
