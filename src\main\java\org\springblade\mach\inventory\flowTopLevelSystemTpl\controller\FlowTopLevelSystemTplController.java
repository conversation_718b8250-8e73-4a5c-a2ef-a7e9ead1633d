package org.springblade.mach.inventory.flowTopLevelSystemTpl.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.entity.FlowTopLevelSystemTplEntity;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.vo.FlowTopLevelSystemTplVO;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.service.IFlowTopLevelSystemTplService;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 顶层系统标准表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-flowTopLevelSystemTpl/flowTopLevelSystemTpl")
@Tag(name = "顶层系统标准表", description = "顶层系统标准表接口")
public class FlowTopLevelSystemTplController extends BladeController {

    private final IFlowTopLevelSystemTplService flowTopLevelSystemTplService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @Operation(summary = "详情", description = "传入flowTopLevelSystemTpl")
    public R<FlowTopLevelSystemTplEntity> detail(@Parameter(description = "主键") @RequestParam Long id) {
        FlowTopLevelSystemTplEntity detail = flowTopLevelSystemTplService.getById(id);
        return R.data(detail);
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @Operation(summary = "分页", description = "传入flowTopLevelSystemTpl")
    public R<IPage<FlowTopLevelSystemTplVO>> page(FlowTopLevelSystemTplVO flowTopLevelSystemTpl, Query query) {
        IPage<FlowTopLevelSystemTplVO> pages = flowTopLevelSystemTplService.selectFlowTopLevelSystemTplPage(Condition.getPage(query), flowTopLevelSystemTpl);
        return R.data(pages);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    @Operation(summary = "列表", description = "传入flowTopLevelSystemTpl")
    public R list(FlowTopLevelSystemTplVO flowTopLevelSystemTpl, Query query) {
        IPage<FlowTopLevelSystemTplVO> pages = flowTopLevelSystemTplService.selectFlowTopLevelSystemTplPage(Condition.getPage(query), flowTopLevelSystemTpl);
        return R.data(pages);
    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @Operation(summary = "新增或修改", description = "传入flowTopLevelSystemTpl")
    public R submit(@Valid @RequestBody FlowTopLevelSystemTplEntity flowTopLevelSystemTpl) {
        return R.status(flowTopLevelSystemTplService.saveOrUpdate(flowTopLevelSystemTpl));
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @Operation(summary = "删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合") @RequestParam String ids) {
        return R.status(flowTopLevelSystemTplService.removeByIds(Func.toLongList(ids)));
    }

	/**
	 * 列表
	 */
	@GetMapping("/dicList")
	public List<FlowTopLevelSystemTplEntity> dicList() {
		return flowTopLevelSystemTplService.dicList();
	}

}
