<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.inventory.flowConsumableCost.mapper.FlowConsumableCostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flowConsumableCostResultMap" type="org.springblade.mach.inventory.flowConsumableCost.pojo.entity.FlowConsumableCostEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectFlowConsumableCostPage" resultMap="flowConsumableCostResultMap">
        select * from flow_consumable_cost where is_deleted = 0
    </select>


    <select id="exportFlowConsumableCost" resultType="org.springblade.mach.inventory.flowConsumableCost.excel.FlowConsumableCostExcel">
        SELECT * FROM flow_consumable_cost ${ew.customSqlSegment}
    </select>

</mapper>
