package org.springblade.mach.inventory.flowTopLevelSystemTpl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.entity.FlowTopLevelSystemTplEntity;
import org.springblade.mach.inventory.flowTopLevelSystemTpl.pojo.vo.FlowTopLevelSystemTplVO;

import java.util.List;

/**
 * 顶层系统标准表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IFlowTopLevelSystemTplService extends BaseService<FlowTopLevelSystemTplEntity> {
    /**
     * 自定义分页
     *
     * @param page
     * @param flowTopLevelSystemTpl
     * @return
     */
    IPage<FlowTopLevelSystemTplVO> selectFlowTopLevelSystemTplPage(IPage<FlowTopLevelSystemTplVO> page, FlowTopLevelSystemTplVO flowTopLevelSystemTpl);

	/**
	 * 查询字典列表
	 * @return
	 */
    List<FlowTopLevelSystemTplEntity> dicList();
}
