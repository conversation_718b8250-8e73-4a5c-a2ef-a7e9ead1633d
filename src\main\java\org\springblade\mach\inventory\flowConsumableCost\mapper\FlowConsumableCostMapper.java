/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowConsumableCost.mapper;

import org.springblade.mach.inventory.flowConsumableCost.pojo.entity.FlowConsumableCostEntity;
import org.springblade.mach.inventory.flowConsumableCost.pojo.vo.FlowConsumableCostVO;
import org.springblade.mach.inventory.flowConsumableCost.excel.FlowConsumableCostExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 耗材成本配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface FlowConsumableCostMapper extends BaseMapper<FlowConsumableCostEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param flowConsumableCost
	 * @return
	 */
	List<FlowConsumableCostVO> selectFlowConsumableCostPage(IPage page, FlowConsumableCostVO flowConsumableCost);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<FlowConsumableCostExcel> exportFlowConsumableCost(@Param("ew") Wrapper<FlowConsumableCostEntity> queryWrapper);

}
