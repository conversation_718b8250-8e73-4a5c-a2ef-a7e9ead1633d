/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.pm.flowRunDetails.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.zhxu.bs.BeanSearcher;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.constant.MachConst;
import org.springblade.common.utils.MachUtils;
import org.springblade.core.log.logger.BladeLogger;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.mach.pm.flowProjectNode.pojo.entity.FlowProjectNodeEntity;
import org.springblade.mach.pm.flowProjectNode.service.IFlowProjectNodeService;
import org.springblade.mach.pm.flowProjectPay.service.IFlowProjectPayService;
import org.springblade.mach.pm.flowRunDetails.excel.FlowRunDetailsExcel;
import org.springblade.mach.pm.flowRunDetails.mapper.FlowRunDetailsMapper;
import org.springblade.mach.pm.flowRunDetails.pojo.entity.FlowRunDetailsEntity;
import org.springblade.mach.pm.flowRunDetails.pojo.vo.FlowRunDetailsVO;
import org.springblade.mach.pm.flowRunDetails.service.IFlowRunDetailsService;
import org.springblade.mach.pm.flowSaleAssistantReal.service.IFlowSaleAssistantRealService;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springblade.mach.pm.projectEqDetails.service.IFlowProjectEqDetailsService;
import org.springblade.mach.pm.projectSchedule.pojo.entity.FlowProjectScheduleEntity;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单套产品详情展示 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
@Transactional(readOnly = true)
public class FlowRunDetailsServiceImpl extends ServiceImpl<FlowRunDetailsMapper, FlowRunDetailsEntity> implements IFlowRunDetailsService {

	@Autowired
	private IUserDeptService userDeptService;

	@Lazy
	@Autowired
	private IFlowProjectEqDetailsService flowProjectEqDetailsService;

	@Autowired
	private BladeLogger bladeLogger;

	@Lazy
	@Autowired
	private IFlowProjectNodeService flowProjectNodeService;

	@Autowired
	private IUserService userService;
	@Autowired
	private IFlowSaleAssistantRealService flowSaleAssistantRealService;


	@Override
	public IPage<FlowRunDetailsVO> selectFlowRunDetailsPage(IPage<FlowRunDetailsVO> page, FlowRunDetailsVO flowRunDetails) {

		String field = flowRunDetails.getField();
		String sortType = flowRunDetails.getSortType();
		String orderBy = "";

		// set排序方式,值为null的排在后面
		if (StringUtil.isNotBlank(field) && StringUtil.isNotBlank(sortType)) {
			if("plan_driver_date".equals(field)) {
				orderBy = field + " is null,CASE " +
					"    WHEN plan_driver_date IS NULL THEN  " +//-- 仅当日期为空时按status排序
					"      CASE a.status " +
					"        WHEN 12 THEN 1  " + //生产制造
					"        WHEN 7  THEN 2  " + //方案图设计
					"        WHEN 11 THEN 3  " + //产品设计
					"        WHEN 15 THEN 4   " + //销售内勤整理附件
					"        WHEN 2  THEN 5   " + //待总经理审核
					"        WHEN 3  THEN 6   " + //技术评级审核
					"        WHEN 5  THEN 7   " + //待销售修改一览表
					"        WHEN 6  THEN 8   " + //分配方案图承接人
					"        WHEN 8  THEN 9   " + //承接部门审核方案图
					"        WHEN 9  THEN 10   " + //承接部门分配节点
					"        WHEN 10 THEN 11   " + //审核承接部门分配节点
					"        WHEN 17 THEN 12   " + //分配承接部门
					"        WHEN 1  THEN 13  " + //待财务审核
					"        WHEN 16 THEN 14  " + //启动发货
					"        WHEN 13 THEN 15  " + //安装调试验收
					"        WHEN 14 THEN 16  " + //质保
					"        WHEN 4  THEN 17  " + //已完成
					"        WHEN 99 THEN 18  " + //已销毁
					"        ELSE 99          " + //未知状态排最后
					"      END " +
					"    ELSE 0 " + // 非空日期记录不参与status排序
					"  END," + StringUtils.replaceAllBlank(field) + " " + (sortType.equalsIgnoreCase("asc") ? "asc" : "desc");
			}else{
				orderBy = field + " is null,"+StringUtils.replaceAllBlank(field) + " " + (sortType.equalsIgnoreCase("asc") ? "asc" : "desc");
			}
		} else {
			//筛选安装调试节点，按照实际发货日期升序排序
			if (FlowProjectScheduleEntity.IDC.equals(String.valueOf(flowRunDetails.getStatus()))) {
				orderBy = "delivery_date is null,delivery_date asc";
			}
			//筛选未发货或工厂按计划发货日期升序排序
			else if (MachConst.NO.getCode().equals(flowRunDetails.getDeliveryStatus()) || StringUtil.isNotBlank(flowRunDetails.getFactoryName())) {
				orderBy = "plan_driver_date is null,plan_driver_date asc";
			} else {
				//logger.info("无需排序");
			}
		}
		if(StrUtil.isNotBlank(orderBy) && !orderBy.contains("a.code")){
			orderBy += ",SUBSTR(a.code,1,4) DESC,SUBSTR(a.code,8,3),SUBSTR(a.code,15,2)";
		}
		flowRunDetails.setOrderBy(orderBy);


		// 获取用户角色
		do{
//			// 外贸内勤
//			if(MachUtils.hasRole("foreignTradeInternalService")){
//				flowRunDetails.setTradeType(MachConst.YES.getCode());//外贸
//			}

			// 国际外贸助理
			if(MachUtils.hasRole("foreignTradeOutService")){
				// 这里注释了，不区分内贸，外贸了
				//flowRunDetails.setTradeType(MachConst.YES.getCode());//外贸
				// 构造可看数据：自己和销售总监
				Long myId = AuthUtil.getUserId();
				List<Long> saleIds = flowSaleAssistantRealService.getSaleIds(myId);
				saleIds.add(myId);
				flowRunDetails.setSaleIdList(saleIds);
				page.setRecords(baseMapper.selectFlowRunDetailsPage(page, flowRunDetails));
				break;
			}

			// 拥有查看全部权限
			boolean showAll = MachUtils.hasPermission("flowRunDetails_viewAll");
			if (showAll) {
				//page = flowRunDetailsService.findPage(page, flowRunDetails);
				page.setRecords(baseMapper.selectFlowRunDetailsPage(page, flowRunDetails));
				break;
			}

			// 销售查看销售总监是自己的
			boolean isSale = MachUtils.isSale();
			if (isSale) {
				//flowRunDetails.setSaleName(AuthUtil.getNickName());
				//page = flowRunDetailsService.findPage(page, flowRunDetails);
				// 构造可看数据：助理和自己的
				Long myId = AuthUtil.getUserId();
				List<Long> saleIds = flowSaleAssistantRealService.getAssistantIds(myId);
				saleIds.add(myId);
				flowRunDetails.setSaleIdList(saleIds);
				page.setRecords(baseMapper.selectFlowRunDetailsPage(page, flowRunDetails));
				break;
			}

			// 查看自己组的
			// 小组组长 或者 有当前部门数据查看权限
			boolean hasOfficeLeader = MachUtils.hasRole("group_leader");
			boolean hasShowCurrentOfficeData = MachUtils.hasPermission("flowRunDetails_viewOffice");
			if(hasOfficeLeader || hasShowCurrentOfficeData){
				String deptId = AuthUtil.getUser().getDeptId();

				// 取到部门下的用户
				List<Long> userIds = userDeptService.findUserIdsByDeptId(Long.valueOf(deptId));
				flowRunDetails.setQyUserIds(userIds);
				//page = flowRunDetailsService.findPage(page, flowRunDetails);
				page.setRecords(baseMapper.selectFlowRunDetailsPage(page, flowRunDetails));
				break;
			}

			// 查看自己参与过的（承接人里包括自己的）
			Long userId = AuthUtil.getUserId();
			flowRunDetails.setQyUserIds(ListUtil.of(userId));
			//page = flowRunDetailsService.findPage(page, flowRunDetails);
			page.setRecords(baseMapper.selectFlowRunDetailsPage(page, flowRunDetails));

		}while(false);

		// 加入付款时间 改成sql读取，这里注释掉了
//		{
//			List<FlowRunDetailsVO> records = page.getRecords();
//			for (FlowRunDetailsVO vo : records) {
//				FlowProjectPayEntity flowProjectPay = new FlowProjectPayEntity();
//				flowProjectPay.setEqDetailsId(vo.getId());
//				flowProjectPay.setType(DictBizCache.getKey("pay_type","单套设备付款方式"));
//				List<FlowProjectPayEntity> payList = flowProjectPayService.list(Condition.getQueryWrapper(flowProjectPay));
//				for (FlowProjectPayEntity pay : payList) {
//					String moneyType = pay.getMoneyType();
//					if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[0])){
//						vo.setType0PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[1])){
//						vo.setType1PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[2])){
//						vo.setType2PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[3])){
//						vo.setType3PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[4])){
//						vo.setType4PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[5])){
//						vo.setType5PayDate(pay.getAccountTime());
//					}else if(StringUtil.equals(moneyType,FlowProjectPayEntity.MONEY_TYPE[6])){
//						vo.setType6PayDate(pay.getAccountTime());
//					}
//				}
//			}
//			page.setRecords(records);
//		}
		//设置是否是总包类外购项目
//		setOutSourced(page);
		//设置图纸计划/交付时间
		setDrawingPlanDeliveryTime(page);
//		//过滤条件IsOutsourced不为空的话，只显示IsOutsourced为1的
//		if(flowRunDetails.getIsOutsourced() != null){
//			List<FlowRunDetailsVO> collect = page.getRecords()
//				.stream().filter(item -> item.getIsOutsourced().equals(flowRunDetails.getIsOutsourced())).collect(Collectors.toList());
//			page.setRecords(collect);
//		}
		return page;
	}

	private void setDrawingPlanDeliveryTime(IPage<FlowRunDetailsVO> page) {
		List<FlowRunDetailsVO> records = page.getRecords();
		if(CollectionUtil.isNotEmpty(records)){
			records.forEach(item -> {
				FlowProjectNodeEntity node = flowProjectNodeService.lambdaQuery()
					.eq(FlowProjectNodeEntity::getProjectDetailId, item.getId())
					.eq(FlowProjectNodeEntity::getNodeType, "1")//机械设计图纸
					.eq(FlowProjectNodeEntity::getType, "2")//设备节点
					.orderByAsc(FlowProjectNodeEntity::getActualDate, FlowProjectNodeEntity::getCreateTime)
					.last("limit 1").one();
				if(node != null && node.getActualDate() != null){
					item.setDrawingsPlanOrDeliveryDate(node.getActualDate());
				}else if(node != null && node.getPlanDate() != null){
					item.setDrawingsPlanOrDeliveryDate(node.getPlanDate());
				}
			});
		}
	}

	private void setOutSourced(IPage<FlowRunDetailsVO> page) {
		List<FlowRunDetailsVO> records = page.getRecords();
		if(CollectionUtil.isNotEmpty(records)){
			//获取到当前页所有code对应的单套产品对象
			List<FlowProjectEqDetailsEntity> list = flowProjectEqDetailsService.lambdaQuery()
				.in(FlowProjectEqDetailsEntity::getCode, records.stream().map(FlowRunDetailsEntity::getCode)
					.collect(Collectors.toList())).list();

			Map<String, FlowProjectEqDetailsEntity> map = list.stream()
				.collect(Collectors.toMap(FlowProjectEqDetailsEntity::getCode, Function.identity()));
			//根据单套产品的projectName判断，如果名字包含总包外购则赋值isOutsourced1，否则赋值0
			records.forEach(item -> {
				FlowProjectEqDetailsEntity flowProjectEqDetailsEntity = map.get(item.getCode());
				if(StringUtil.isNotBlank(flowProjectEqDetailsEntity.getProjectName())
					&& flowProjectEqDetailsEntity.getProjectName().contains("总包类外购项目")){
					item.setIsOutsourced("1");//总包类外购项目
				}else{
					item.setIsOutsourced("0");
				}
			});
		}
	}


	@Override
	public List<FlowRunDetailsExcel> exportFlowRunDetails(FlowRunDetailsVO flowRunDetails) {
		//List<FlowRunDetailsExcel> flowRunDetailsList = baseMapper.exportFlowRunDetails(flowRunDetailsVO);

		IPage<FlowRunDetailsVO> page = new Page<FlowRunDetailsVO>();
		page.setSize(-1);// 不分页
		List<FlowRunDetailsVO> records = this.selectFlowRunDetailsPage(page,flowRunDetails).getRecords();
		List<FlowRunDetailsExcel> flowRunDetailsList = records.stream().map(a -> {
			FlowRunDetailsExcel bean = Objects.requireNonNull(BeanUtil.copyProperties(a, FlowRunDetailsExcel.class));
			// 转换签订主体
			if(StringUtil.isNotBlank(bean.getCompany())){
				String company = DictBizCache.getValue("company", Integer.valueOf(bean.getCompany()));
				bean.setCompany(company);
			}
			// 转换生产基地
			if(StringUtil.isNotBlank(bean.getFactoryName())){
				StringBuilder factoryNameStr = new StringBuilder();
				Integer[] factoryNames = Func.toIntArray(bean.getFactoryName());
				for (Integer factoryCode : factoryNames) {
					String factoryName = DictBizCache.getValue("factory_name", factoryCode);
					if(factoryNameStr.length() > 0){
						factoryNameStr.append(",");
					}
					factoryNameStr.append(factoryName);
				}
				bean.setFactoryName(factoryNameStr.toString());
			}
			// 转换设备颜色
			if("99".equals(bean.getEquipmentColor())){
				bean.setEquipmentColor(bean.getCustomColor());
			} else {
				if(StringUtil.isNotBlank(bean.getEquipmentColor())) {
					String equipmentColor = DictBizCache.getValue("equipment_color", Integer.valueOf(bean.getEquipmentColor()));
					bean.setEquipmentColor(equipmentColor);
				}
			}
			// 转换发货状态
			if(StringUtil.isNotBlank(bean.getDeliveryStatus())){
				String deliver_goods = DictBizCache.getValue("deliver_goods", Integer.valueOf(bean.getDeliveryStatus()));
				bean.setDeliveryStatus(deliver_goods);
			}
			// 当前状态
			if(StringUtil.isNotBlank(bean.getStatus())){
				String status = DictBizCache.getValue("equipment_status", Integer.valueOf(bean.getStatus()));
				bean.setStatus(status);
			}
			return bean;
		}).collect(Collectors.toList());
		return flowRunDetailsList;
	}

	/**
	 * 获取进度详情表数据根据单套产品id
	 * @param flowRunDetails
	 * @return
	 */
    @Override
    public FlowRunDetailsEntity getProgress(FlowRunDetailsEntity flowRunDetails) {
        return baseMapper.getProgress(flowRunDetails);
    }

	/**
	 * 更新进度详情表生产进度
	 * @param flowRunDetails
	 */
	@Override
	@Transactional(readOnly = false)
	public void updateProduction(FlowRunDetailsEntity flowRunDetails) {
		baseMapper.updateProduction(flowRunDetails);
	}

	@Transactional(readOnly = false)
	public void saved(FlowRunDetailsEntity flowRunDetails){
		baseMapper.save(flowRunDetails);
	}

	@Transactional(readOnly = false)
	public void update(FlowRunDetailsEntity flowRunDetails){
		baseMapper.update(flowRunDetails);
	}

	@Transactional(readOnly = false)
	public void delete(FlowRunDetailsEntity flowRunDetails){
		baseMapper.delete(flowRunDetails);
	}

	/**
	 * 更新安装调试验收进度
	 * @param eqDetailsId
	 * @param rate
	 */
	@Transactional(readOnly = false)
	public void undateIDC(Long eqDetailsId, double rate) {
		FlowRunDetailsEntity flowRunDetails = new FlowRunDetailsEntity();
		flowRunDetails.setId(eqDetailsId);
		FlowRunDetailsEntity progress = getProgress(flowRunDetails);
		progress.setInstallProgress(rate);
		this.saved(progress);
	}

	@Override
	@Transactional(readOnly = false)
	public R updateData(FlowRunDetailsEntity flowRunDetails) {
		FlowRunDetailsEntity runDetails = this.getById(flowRunDetails.getId());

		//修改发货时间后将通知状态置0
		if (flowRunDetails.getPlanDriverDate() != null && !flowRunDetails.getPlanDriverDate().equals(runDetails.getPlanDriverDate())) {
			flowProjectEqDetailsService.updateNotify(flowRunDetails.getId(), MachConst.NO.getCode());
		}
		FlowRunDetailsEntity progress = this.getProgress(flowRunDetails);
		progress.setPlanDriverDate(flowRunDetails.getPlanDriverDate());
		progress.setComment(flowRunDetails.getComment());
		progress.setRemarks(flowRunDetails.getRemarks());
		progress.setProductionProgress(flowRunDetails.getProductionProgress());
		progress.setInstallProgress(flowRunDetails.getInstallProgress());
		this.saveOrUpdate(progress);

		bladeLogger.info("修改项目运行情况["+runDetails.getCode()+"]", JSONUtil.toJsonStr(progress));

		return R.success("更新成功");
	}

	/**
	 * 重写父类
	 * @param entity 实体对象
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public boolean saveOrUpdate(FlowRunDetailsEntity entity) {
		if(null == entity)
			return false;
		if(entity.getId() > 0){
			return this.updateFlowRunDetails(entity);
		}else{
			return this.saveFlowRunDetails(entity);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public boolean saveFlowRunDetails(FlowRunDetailsEntity entity) {
		int r = baseMapper.saveFlowRunDetails(entity);
		return r>0;
	}

	@Override
	@Transactional(readOnly = false)
	public boolean updateFlowRunDetails(FlowRunDetailsEntity entity) {
		int r = baseMapper.updateFlowRunDetails(entity);
		return r > 0;
	}

	/**
	 * 获取项目运行情况-统计信息
	 * @return
	 */
	@Override
	public R getStatData() {
		Kv model = Kv.init();
		FlowRunDetailsVO deliveryTotal = this.deliveryTotal();
		List<FlowRunDetailsVO> deliveryFactory = this.deliveryFactory();
		model.set("deliveryTotal", deliveryTotal);
		model.set("deliveryFactory", deliveryFactory);
		return R.data(model,"获取成功");
	}

	/**
	 * 获取本年度发货的产品数量与全部未发货的数量
	 * @return
	 */
	@Override
	public FlowRunDetailsVO deliveryTotal() {
		return baseMapper.deliveryTotal();
	}


	/**
	 * 各个工厂本月待发货的数量与下月待发货的数量
	 * @return
	 */
	@Override
	public List<FlowRunDetailsVO> deliveryFactory() {
		return baseMapper.deliveryFactory();
	}

	/**
	 * 查询大屏数据,验收前的数据
	 * @return
	 */
    @Override
    public List<FlowRunDetailsVO> findDataViewList() {
		return baseMapper.findDataViewList();
	}

}
