<template>
    <!--
        组件：付款审批单详情
        路径：src\views\mach\pm\flowContract\flowApvContractPayInfo\apvContractPayInfoDetails.vue
    -->
  <el-row :gutter="10">
    <!-- 左侧 -->
    <el-col :span="12">
        <!-- 附件内容 -->
        <div style="border:1px solid #eee;">
            <el-card>
                <template #header>
                    <span>附件内容</span>
                </template>
                <!-- Placeholder for attachments. This might need to be a list or a different component -->
                                    <div style="width:100%;height:1100px;">
                                        <div v-if="loading" class="pdf-loading">
                                            <a-spin tip="正在加载合同文件..."/>
                                        </div>
                                        <div v-else-if="pdfError" class="pdf-error">
                                            <a-empty description="暂无合同文件或加载失败" />
                                        </div>
                                        <mach-pdf
                                            v-else
                                            :src="getCombinedContractPdfUrl()"
                                            @load="loading = false"
                                            @error="handlePdfError"
                                        ></mach-pdf>
                                    </div>
            </el-card>
        </div>
    </el-col>
    <!-- 右侧 -->
    <el-col :span="12">
        <!-- 付款审批单详情 -->
        <div style="border:1px solid #eee;">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>付款审批单详情</span>
                    </div>
                </template>
                <!-- 内容 -->
                <div>
                    <!-- 流转信息 -->
                    <template v-if="form.apvContractPayInfo && form.apvContractPayInfo.procInsId">
                        <mach-card :id="'流转信息'" :open="true">
                            <template #title>
                                <el-icon size="20"><Search/></el-icon>
                                <el-text size="large">流转信息</el-text>
                            </template>
                            <histoicFlowVue
                            v-if="form.apvContractPayInfo.procInsId"
                            :process-instance-id="form.apvContractPayInfo.procInsId"></histoicFlowVue>
                        </mach-card>
                        <br/>
                    </template>

                    <!-- 付款审批单信息 -->
                    <div style="width:100%;height:1100px;">
                        <mach-pdf :src="form.pdfUrl"></mach-pdf> <!-- Main document PDF -->
                    </div>
                    <br/>

                    <!-- 请款单列表 -->
                    <mach-card :id="'请款单列表'" :open="true" v-if="form.apvContractPayInfo.paymentRealList && form.apvContractPayInfo.paymentRealList.length > 0" style="margin-bottom: 16px;">
                        <template #title>
                            <el-icon size="20"><Money/></el-icon>
                            <el-text size="large">请款单列表</el-text>
                        </template>
                        <el-table
                            :data="form.apvContractPayInfo.paymentRealList"
                            border
                            stripe
                            v-loading="loading"
                            :empty-text="loading ? '加载中...' : '暂无请款单数据'">
                            <el-table-column prop="partyB" label="收款方" min-width="120"></el-table-column>
                            <el-table-column prop="contractCode" label="合同编号" min-width="120"></el-table-column>
                            <el-table-column prop="eqAmount" label="合同总额" width="120">
                                <template #default="{ row }">
                                    {{ formatAmount(row.eqAmount) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="eqPaymentAmount" label="付款金额" width="120">
                                <template #default="{ row }">
                                    {{ formatAmount(row.eqPaymentAmount) }}
                                </template>
                            </el-table-column>
                            <el-table-column label="合同文件" width="100">
                                <template #default="{ row }">
                                    <el-button
                                        v-if="row.contractFiles"
                                        type="primary"
                                        link
                                        @click="previewFile(row.contractFiles)">
                                        查看合同
                                    </el-button>
                                    <span v-else>无合同</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="附件" width="100">
                                <template #default="{ row }">
                                    <el-button
                                        v-if="row.attachmentFiles"
                                        type="primary"
                                        link
                                        @click="previewFile(row.attachmentFiles)">
                                        查看附件
                                    </el-button>
                                    <span v-else>无附件</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="remarks" label="备注" min-width="150"></el-table-column>
                        </el-table>
                        <div style="margin-top: 10px;">
                            <el-text type="primary">共 {{form.apvContractPayInfo.paymentRealList.length}} 条请款单</el-text>
                            <el-text type="primary" style="margin-left: 20px;">
                                总金额: {{formatAmount(form.apvContractPayInfo.paymentRealList.reduce((sum, item) => sum + (Number(item.eqPaymentAmount) || 0), 0))}}
                            </el-text>
                        </div>
                    </mach-card>

                    <!-- 支付方式信息已移除，根据需求只显示请款单列表 -->

                    <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
                        <!-- 表单：xxx -->
                        <!--
                        <template #xxx="{row,disabled}">
                            <span>{{ row.xxx }}</span>
                        </template>
                        -->
                    </avue-form>

                </div>
            </el-card>
        </div>
    </el-col>
  </el-row>
</template>

<script setup>
  import { ref, getCurrentInstance, computed } from 'vue';
  import { useStore } from 'vuex';
  import { Search, Money, CreditCard } from '@element-plus/icons-vue';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { getDetail } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import { getDictionary } from '@/api/system/dict';
  import { getFileLink } from '@/api/resource/oss';
  import { Base64 } from 'js-base64';
  import website from '@/config/website';
  import { getToken } from '@/utils/auth';
  import { checkIsWeixin } from '@/utils/util';
  import dayjs from 'dayjs';
  import { ElMessageBox } from 'element-plus';

  // 格式化金额
  const formatAmount = (amount) => {
    if (!amount && amount !== 0) return '--';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // 格式化日期
  const formatDate = (date) => {
    if (!date) return '--';
    return dayjs(date).format('YYYY-MM-DD');
  };

  // 支付方式字典
  const payTypeDict = ref([]);
  const getPayTypeDict = async () => {
    const res = await getDictionary({ code: 'pay_type' });
    if (res.data.data) {
      payTypeDict.value = res.data.data;
    }
  };
  getPayTypeDict();

  // 获取支付方式名称
  const getPayTypeName = (type) => {
    const item = payTypeDict.value.find(item => item.dictKey === type);
    return item ? item.dictValue : type;
  };

  // 预览文件
  const previewFile = async (files) => {
    if (!files) return;

    const fileList = typeof files === 'string' ? JSON.parse(files) : files;
    if (!fileList || fileList.length === 0) {
      ElMessageBox.alert('没有可预览的文件', '提示');
      return;
    }

    const handlePreview = async (file) => {
      try {
        const response = await getFileLink(file.link);
        if (response.data.code === 200) {
          const fileUrl = decodeURIComponent(response.data.data);
          const tokenParam = `${website.tokenHeader}=bearer ${getToken()}`;
          const fullFileUrl = fileUrl + (fileUrl.includes('?') ? '&' : '?') + tokenParam;
          const previewUrl = encodeURIComponent(Base64.encode(fullFileUrl));
          const watermarkTxt = encodeURIComponent(store.getters.userInfo.real_name + ' ' + dayjs().format('YY-MM-DD') + ' ' + store.getters.userInfo.account);
          const filePreviewFullUrl = `${website.kkFileViewUrl}/onlinePreview?url=${previewUrl}&watermarkTxt=${watermarkTxt}`;

          // 检查是否在微信浏览器中
          const isWeixin = checkIsWeixin();
          if (isWeixin) {
            // 在微信中使用弹窗预览
            ElMessageBox.alert('<iframe src="' + filePreviewFullUrl + '" style="width:100%;height:600px;border:0;"></iframe>', '文件预览', {
              dangerouslyUseHTMLString: true,
              customClass: 'preview-dialog'
            });
          } else {
            // 在其他浏览器中打开新窗口
            window.open(filePreviewFullUrl, '_blank');
          }
        } else {
          ElMessageBox.alert(response.data.msg || '预览失败', '错误');
        }
      } catch (error) {
        ElMessageBox.alert('文件预览失败：' + (error.message || '未知错误'), '错误');
      }
    };

    if (fileList.length === 1) {
      // 如果只有一个文件，直接预览
      await handlePreview(fileList[0]);
    } else {
      // 如果有多个文件，让用户选择
      const fileNames = fileList.map(file => file.name);
      try {
        const selectedFileName = await ElMessageBox.prompt('请选择要预览的文件', '选择文件', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'select',
          inputValue: fileNames[0],
          inputPlaceholder: '请选择文件',
          inputValidator: (value) => !!value,
          inputErrorMessage: '请选择要预览的文件',
          inputOptions: fileNames.map((name, index) => ({
            label: name,
            value: name
          }))
        });

        const selectedFile = fileList.find(file => file.name === selectedFileName.value);
        if (selectedFile) {
          await handlePreview(selectedFile);
        }
      } catch (error) {
        // 用户取消选择，不做任何操作
      }
    }
  };

  let { proxy } = getCurrentInstance();

  const props = defineProps({
    businessId: {},
    taskId:{},
    processInstanceId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });

  const store = useStore();
  const loading = ref(false);
  const pdfError = ref(false);
  const form = ref({ apvContractPayInfo: {}, pdfUrl: null, attachmentUrl: null });
  const auditOption = ref({});
  const auditForm = ref({
    comment: ""
  });

  auditOption.value = {
    submitBtn: false,
    emptyBtn: false,
    labelWidth: 100,
    column: [
        {
            label: "备注",
            prop: "comment",
            type: "textarea",
            maxlength: 1800,
            showWordLimit: true,
            row: true,
            span: 24,
        },
    ]
  };

  function init(){
    if (!props.businessId) return;

    loading.value = true;
    getDetail(props.businessId).then(res => {
        const apiResponse = res.data;
        const payload = apiResponse ? apiResponse.data : null;
        debugger;
        if (payload && typeof payload === 'object') {
            // 初始化表单数据
            form.value = {
                apvContractPayInfo: {},
                pdfUrl: null,
                attachmentUrl: null
            };

            // 处理主数据
            let mainData = payload.apvContractPayInfo || payload;
            form.value.apvContractPayInfo = mainData;

            // 设置PDF URL
            if (mainData && mainData.id) {
                form.value.pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id=${mainData.id}`;
                form.value.attachmentUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewAttachmentsPdfOutStream?id=${mainData.id}`;
            }

            // 确保请款单列表数据存在
            if (!form.value.apvContractPayInfo.paymentRealList) {
                form.value.apvContractPayInfo.paymentRealList = [];
            }

            // 处理请款单列表数据
            form.value.apvContractPayInfo.paymentRealList = form.value.apvContractPayInfo.paymentRealList.map(item => {
                // 确保文件数据是对象格式
                if (item.contractFiles && typeof item.contractFiles === 'string') {
                    try {
                        item.contractFiles = JSON.parse(item.contractFiles);
                    } catch (e) {
                        console.warn('解析合同文件数据失败:', e);
                        item.contractFiles = null;
                    }
                }
                if (item.attachmentFiles && typeof item.attachmentFiles === 'string') {
                    try {
                        item.attachmentFiles = JSON.parse(item.attachmentFiles);
                    } catch (e) {
                        console.warn('解析附件文件数据失败:', e);
                        item.attachmentFiles = null;
                    }
                }
                console.log("item contractFiles:",item.contractFiles);
                return item;
            });

            // 处理备注显示
            const commentOption = proxy.findObject(auditOption.value.column, 'comment');
            if (commentOption) {
                commentOption.display = !!(mainData && mainData.audit);
            }
        } else {
            console.error("API payload is invalid:", payload);
            proxy.$message.error('加载审批单详情失败: 无效的数据返回');
        }
    }).catch(error => {
        console.error("Error fetching apvContractPayInfo details:", error);
        proxy.$message.error('加载审批单详情失败: 请求处理异常');
    }).finally(() => {
        loading.value = false;
    });
  }

  // 获取合并后的合同PDF URL

  const getCombinedContractPdfUrl = () => {
    try {
      if (!form.value.apvContractPayInfo || !form.value.apvContractPayInfo.paymentRealList) {
        pdfError.value = true;
        return '';
      }

      // 检查是否有合同文件
      const hasContractFiles = form.value.apvContractPayInfo.paymentRealList.some(item => {
        if (!item.contractFiles) return false;

        try {
          const files = typeof item.contractFiles === 'string'
            ? JSON.parse(item.contractFiles)
            : item.contractFiles;
          return files && files.length > 0;
        } catch (e) {
          console.warn('解析合同文件信息失败:', e);
          return false;
        }
      });

      console.log("请款单列表：", form.value.apvContractPayInfo.paymentRealList);
      console.log("是否有合同文件：", hasContractFiles);

      // 如果没有合同文件，显示错误状态
      if (!hasContractFiles) {
        pdfError.value = true;
        proxy.$message.warning('未找到相关合同文件');
        return '';
      }

      pdfError.value = false;
      // 直接使用付款审批单ID，后端会自动获取所有相关的合同文件
      return `/blade-apvContractPayInfo/apvContractPayInfo/viewAttachmentsPdfOutStream?id=${form.value.apvContractPayInfo.id}`;
    } catch (error) {
      console.error('获取合同PDF URL失败:', error);
      pdfError.value = true;
      proxy.$message.error('获取合同文件失败');
      return '';
    }
  };

  if (props.businessId) {
      loading.value = true;
      init();
  }

  function handleCancel() {
    proxy.$emit('handleCancel');
  }

  function handlePdfError() {
    loading.value = false;
    pdfError.value = true;
    proxy.$message.error('合同文件加载失败');
  }

</script>

<style lang="scss" scoped>
.pdf-loading,
.pdf-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.pdf-error {
  :deep(.ant-empty) {
    margin: 32px 0;
    .ant-empty-description {
      color: #999;
    }
  }
}

.preview-dialog {
  :deep(.el-message-box__content) {
    padding: 10px;
  }
}
</style>