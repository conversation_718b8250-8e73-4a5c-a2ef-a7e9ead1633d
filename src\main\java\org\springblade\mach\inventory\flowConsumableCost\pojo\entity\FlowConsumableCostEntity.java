/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.inventory.flowConsumableCost.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 耗材成本配置 实体类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@TableName("flow_consumable_cost")
@Schema(description = "FlowConsumableCost对象")
@EqualsAndHashCode(callSuper = true)
public class FlowConsumableCostEntity extends BaseEntity {

	/**
	 * 名称
	 */
	@Schema(description = "名称")
	private String name;
	/**
	 * 编码
	 */
	@Schema(description = "编码")
	private String code;
	/**
	 * 单价
	 */
	@Schema(description = "单价")
	private BigDecimal unitPrice;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;

	@TableField(exist = false)
	private Integer status;

}
