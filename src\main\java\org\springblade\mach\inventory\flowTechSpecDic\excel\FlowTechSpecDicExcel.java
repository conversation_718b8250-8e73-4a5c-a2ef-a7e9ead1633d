/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.inventory.flowTechSpecDic.excel;


import lombok.Data;

import java.lang.Double;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 技术参数字典 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FlowTechSpecDicExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 设备系统id
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备系统id")
	private Long systemId;
	/**
	 * 名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("名称")
	private String name;
	/**
	 * 编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("编码")
	private String code;
	/**
	 * 名称展示函数
	 */
	@ColumnWidth(20)
	@ExcelProperty("名称展示函数")
	private String nameFn;
	/**
	 * 值可选项
	 */
	@ColumnWidth(20)
	@ExcelProperty("值可选项")
	private String selects;
	/**
	 * 值展示函数
	 */
	@ColumnWidth(20)
	@ExcelProperty("值展示函数")
	private String valFn;
	/**
	 * 单位
	 */
	@ColumnWidth(20)
	@ExcelProperty("单位")
	private String unit;
	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remarks;
	/**
	 * 等级
	 */
	@ColumnWidth(20)
	@ExcelProperty("等级")
	private String level;
	/**
	 * 展示函数
	 */
	@ColumnWidth(20)
	@ExcelProperty("展示函数")
	private String showFn;
	/**
	 * 行号
	 */
	@ColumnWidth(20)
	@ExcelProperty("行号")
	private Integer row;
	/**
	 * 排序
	 */
	@ColumnWidth(20)
	@ExcelProperty("排序")
	private Double sortOrder;

}
