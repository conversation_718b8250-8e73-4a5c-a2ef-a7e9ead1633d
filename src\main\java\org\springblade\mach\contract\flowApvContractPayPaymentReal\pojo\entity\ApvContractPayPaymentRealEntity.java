/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 付款单与款单关联表 实体类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@TableName("flow_apv_contract_pay_payment_real")
@Schema(description = "ApvContractPayPaymentReal对象")
@EqualsAndHashCode(callSuper = true)
public class ApvContractPayPaymentRealEntity extends BaseEntity {

	/**
	 * 付款方
	 */
	@Schema(description = "付款方")
	private String partyA;
	/**
	 * 收款方
	 */
	@Schema(description = "收款方")
	private String partyB;
	/**
	 * 合同编号
	 */
	@Schema(description = "合同编号")
	private String contractCode;
	/**
	 * 单套产品合同总额
	 */
	@Schema(description = "单套产品合同总额")
	private String eqAmount;
	/**
	 * 单套产品付款金额
	 */
	@Schema(description = "单套产品付款金额")
	private String eqPaymentAmount;
	/**
	 * 付款单ID
	 */
	@Schema(description = "付款单ID")
	private Long payInfoId;
	/**
	 * 备注信息
	 */
	@Schema(description = "备注信息")
	private String remarks;

	/**
	 * 合同文件(单个文件)
	 */
	@Schema(description = "合同文件(单个文件)")
	private String contractFiles;

	/**
	 * 附件文件(多个文件用逗号分隔)
	 */
	@Schema(description = "附件文件(多个文件用逗号分隔)")
	private String attachmentFiles;

}
