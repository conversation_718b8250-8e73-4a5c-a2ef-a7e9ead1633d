/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowTechSpecDic.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.mach.inventory.flowTechSpecDic.pojo.entity.FlowTechSpecDicEntity;
import org.springblade.mach.inventory.flowTechSpecDic.pojo.vo.FlowTechSpecDicVO;
import org.springblade.mach.inventory.flowTechSpecDic.excel.FlowTechSpecDicExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 技术参数字典 服务类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IFlowTechSpecDicService extends IService<FlowTechSpecDicEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param flowTechSpecDic
	 * @return
	 */
	IPage<FlowTechSpecDicVO> selectFlowTechSpecDicPage(IPage<FlowTechSpecDicVO> page, FlowTechSpecDicVO flowTechSpecDic);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<FlowTechSpecDicExcel> exportFlowTechSpecDic(Wrapper<FlowTechSpecDicEntity> queryWrapper);

	/**
	 * 获取最大行号
	 * @return
	 */
	Integer getMaxRow(Long systemId);

	/**
	 * 根据systemId获取技术参数字典
	 * @param systemId
	 * @return
	 */
	List<FlowTechSpecDicEntity> getTechSpecListBySystemId(Long systemId);
}
