package org.springblade.mach.contract.flowApvContractPayDetail.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.utils.LogUtils;
import org.springblade.common.utils.MachUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.mach.contract.flowApvContractPayDetail.excel.ApvContractPayDetailExcel;
import org.springblade.mach.contract.flowApvContractPayDetail.mapper.ApvContractPayDetailMapper;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.vo.ApvContractPayDetailVO;
import org.springblade.mach.contract.flowApvContractPayDetail.service.IApvContractPayDetailService;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;
import org.springblade.mach.contract.flowApvContractPayInfo.service.IApvContractPayInfoService;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 付款审批支付明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@AllArgsConstructor
@Service
public class ApvContractPayDetailServiceImpl extends BaseServiceImpl<ApvContractPayDetailMapper, ApvContractPayDetailEntity> implements IApvContractPayDetailService {


	@Override
	public IPage<ApvContractPayDetailVO> selectApvContractPayDetailPage(IPage<ApvContractPayDetailVO> page, ApvContractPayDetailVO apvContractPayDetail) {
		return page.setRecords(baseMapper.selectApvContractPayDetailPage(page, apvContractPayDetail));
	}


	@Override
	public List<ApvContractPayDetailExcel> exportApvContractPayDetail(Wrapper<ApvContractPayDetailEntity> queryWrapper) {
		List<ApvContractPayDetailExcel> apvContractPayDetailList = baseMapper.exportApvContractPayDetail(queryWrapper);
		//apvContractPayDetailList.forEach(apvContractPayDetail -> {
		//	apvContractPayDetail.setTypeName(DictCache.getValue(DictEnum.YES_NO, ApvContractPayDetail.getType()));
		//});
		return apvContractPayDetailList;
	}



}
