<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.contract.flowApvContractPayPaymentReal.mapper.ApvContractPayPaymentRealMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="apvContractPayPaymentRealResultMap" type="org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity">
        <result column="id" property="id"/>
        <result column="party_a" property="partyA"/>
        <result column="party_b" property="partyB"/>
        <result column="contract_code" property="contractCode"/>
        <result column="eq_amount" property="eqAmount"/>
        <result column="eq_payment_amount" property="eqPaymentAmount"/>
        <result column="pay_info_id" property="payInfoId"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectApvContractPayPaymentRealPage" resultMap="apvContractPayPaymentRealResultMap">
        select * from flow_apv_contract_pay_payment_real where is_deleted = 0
    </select>


    <select id="exportApvContractPayPaymentReal" resultType="org.springblade.mach.contract.flowApvContractPayPaymentReal.excel.ApvContractPayPaymentRealExcel">
        SELECT * FROM flow_apv_contract_pay_payment_real ${ew.customSqlSegment}
    </select>

</mapper>
