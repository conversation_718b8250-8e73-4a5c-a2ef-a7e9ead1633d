<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.inventory.flowMaterialInfo.mapper.FlowMaterialInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flowMaterialInfoResultMap" type="org.springblade.mach.inventory.flowMaterialInfo.pojo.entity.FlowMaterialInfoEntity">
        <result column="id" property="id"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="spec_model" property="specModel"/>
        <result column="unit" property="unit"/>
        <result column="brand" property="brand"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="pricing_mode" property="pricingMode"/>
        <result column="model_description" property="modelDescription"/>
        <result column="material_category" property="materialCategory"/>
        <result column="material_subcategory" property="materialSubcategory"/>
        <result column="material_application" property="materialApplication"/>
        <result column="other_description" property="otherDescription"/>
        <result column="material_standard" property="materialStandard"/>
        <result column="material_material" property="materialMaterial"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="min_temp" property="minTemp"/>
        <result column="max_temp" property="maxTemp"/>
        <result column="min_humidity" property="minHumidity"/>
        <result column="max_humidity" property="maxHumidity"/>
        <result column="shelf_life" property="shelfLife"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectFlowMaterialInfoPage" resultMap="flowMaterialInfoResultMap">
        select * from flow_material_info where is_deleted = 0
    </select>


    <select id="exportFlowMaterialInfo" resultType="org.springblade.mach.inventory.flowMaterialInfo.excel.FlowMaterialInfoExcel">
        SELECT * FROM flow_material_info ${ew.customSqlSegment}
    </select>

</mapper>
