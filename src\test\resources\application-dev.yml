#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 1
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  #排除DruidDataSourceAutoConfigure
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************************************************************************************************************************************
          username: root
          password: root
        crm:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************************************************************
          username: root
          password: root

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://127.0.0.1:9000
  #oss转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: http://127.0.0.1:8280/minio
  #访问key
  access-key: minioadmin
  #密钥key
  secret-key: minioadmin
  #存储桶
  bucket-name: machflow

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: true
    ##redis服务地址
    address: redis://127.0.0.1:6379
    password:
    database: 1
  #本地文件上传
  file:
    remote-mode: false
    #upload-domain: http://localhost:8999
    #remote-path: /usr/share/nginx/html
    realPath: D:/machflowfiles
    upload-path: /userfiles

  # 保存用户最后操作时间
  optime:
    enabled: true

#系统邮件账号密码
sys:
  email:
    account:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    password:
      - "Mach2023#"
      - 198633Minghui
      - Mach2024@Tech

#定时任务配置
scheduling:
  enabled: false
  # 易快报上传项目
  uploadAllItems:
    cron: '-'
    # cron: '0 0/1 * * * ?'
  # 插入马赫客服系统项目一览表
  insertMachCRMSchedule:
    cron: '-'
    # cron: '0 0/1 * * * ?'
  # 每天9点，发送项目延期通知函
  notifySaleDelayProject:
    cron: '-'
    # cron: '0 0 9 * * ?'
  # 每天9点10分，发送投产确认邮件
  notifySaleConfirmProduceProject:
    cron: '-'
#    cron: '0 10 9 * * ?'
  # 每天0点30分，检查投产确认邮件回复
  checkSaleConfirmProduceReply:
    cron: '-'
#    cron: '0 0/31 * * * ?'
  # 定时发送发货通知邮件
  deliveryNotifyEmail:
    cron: '-'
#    cron: '0 0/10 * * * ?'
  # 每月20日,生成截止当天0点未生成内部合同
  autoCreateInnerContract:
    cron: '-'
#    cron: '0 05 9 20 * ?'
  # 自动提交合同请款单
  autoSubmitContractPayment:
    cron: '-'
#    cron: '0 0/45 * * * ?'
  # 每天7.20检查可启动加工合同质检的流程
  processingContractStartQc:
    cron: '-'
#    cron: '0 20 7 * * ?'
#马赫相关配置
mach:
  drawing: #图纸系统
    auto-login-url: http://192.168.1.159:1888/oauth/redirect/?code={code}&state={state}
    sign-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
