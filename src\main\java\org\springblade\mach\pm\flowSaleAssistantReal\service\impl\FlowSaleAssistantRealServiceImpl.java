/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.pm.flowSaleAssistantReal.service.impl;

import org.springblade.common.cache.UserCache;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.mach.pm.flowSaleAssistantReal.cache.FlowSaleAssistantRealCache;
import org.springblade.mach.pm.flowSaleAssistantReal.pojo.entity.FlowSaleAssistantRealEntity;
import org.springblade.mach.pm.flowSaleAssistantReal.pojo.vo.FlowSaleAssistantRealCacheVo;
import org.springblade.mach.pm.flowSaleAssistantReal.pojo.vo.FlowSaleAssistantRealVO;
import org.springblade.mach.pm.flowSaleAssistantReal.excel.FlowSaleAssistantRealExcel;
import org.springblade.mach.pm.flowSaleAssistantReal.mapper.FlowSaleAssistantRealMapper;
import org.springblade.mach.pm.flowSaleAssistantReal.service.IFlowSaleAssistantRealService;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售与助理关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Service
public class FlowSaleAssistantRealServiceImpl extends BaseServiceImpl<FlowSaleAssistantRealMapper, FlowSaleAssistantRealEntity> implements IFlowSaleAssistantRealService {

	@Override
	public IPage<FlowSaleAssistantRealVO> selectFlowSaleAssistantRealPage(IPage<FlowSaleAssistantRealVO> page, FlowSaleAssistantRealVO flowSaleAssistantReal) {
		return page.setRecords(baseMapper.selectFlowSaleAssistantRealPage(page, flowSaleAssistantReal));
	}


	@Override
	public List<FlowSaleAssistantRealExcel> exportFlowSaleAssistantReal(Wrapper<FlowSaleAssistantRealEntity> queryWrapper) {
		List<FlowSaleAssistantRealExcel> flowSaleAssistantRealList = baseMapper.exportFlowSaleAssistantReal(queryWrapper);
		//flowSaleAssistantRealList.forEach(flowSaleAssistantReal -> {
		//	flowSaleAssistantReal.setTypeName(DictCache.getValue(DictEnum.YES_NO, FlowSaleAssistantReal.getType()));
		//});
		return flowSaleAssistantRealList;
	}

    @Override
    public boolean submit(FlowSaleAssistantRealEntity flowSaleAssistantReal) {
		if(null == flowSaleAssistantReal.getSaleId() || null == flowSaleAssistantReal.getUserId()){
			return false;
		}
		// 放入销售信息
		User sale = UserCache.getUser(flowSaleAssistantReal.getSaleId());
		flowSaleAssistantReal.setSaleName(sale.getRealName());
		flowSaleAssistantReal.setSaleAccount(sale.getAccount());
		// 放入助理信息
		User user = UserCache.getUser(flowSaleAssistantReal.getUserId());
		flowSaleAssistantReal.setUserName(user.getRealName());
		flowSaleAssistantReal.setUserAccount(user.getAccount());

		super.saveOrUpdate(flowSaleAssistantReal);
		// 清除缓存
		FlowSaleAssistantRealCache.removeCache();
        return true;
    }

	/**
	 * 根据销售ID获取助理IDS
	 * @param saleId
	 * @return
	 */
	@Override
	public List<Long> getAssistantIds(Long saleId) {
		List<FlowSaleAssistantRealCacheVo> cacheList = FlowSaleAssistantRealCache.getList();
		return cacheList.stream().filter(item -> item.getSaleId().equals(saleId)).map(FlowSaleAssistantRealCacheVo::getUserId).collect(Collectors.toList());
//		List<FlowSaleAssistantRealEntity> list = this.lambdaQuery().select(FlowSaleAssistantRealEntity::getUserId).eq(FlowSaleAssistantRealEntity::getSaleId, saleId).list();
//		return list.stream().map(FlowSaleAssistantRealEntity::getUserId).collect(Collectors.toList());
	}

	/**
	 * 根据助理ID获取销售IDS
	 * @param assistantId
	 * @return
	 */
	@Override
	public List<Long> getSaleIds(Long assistantId) {
		List<FlowSaleAssistantRealCacheVo> cacheList = FlowSaleAssistantRealCache.getList();
		return cacheList.stream()
			.filter(item -> item.getUserId().equals(assistantId))
			.map(FlowSaleAssistantRealCacheVo::getSaleId)
			.collect(Collectors.toList());
//		List<FlowSaleAssistantRealEntity> list = this.lambdaQuery().select(FlowSaleAssistantRealEntity::getSaleAccount).eq(FlowSaleAssistantRealEntity::getUserId, assistantId).list();
//		return list.stream().map(FlowSaleAssistantRealEntity::getSaleId).collect(Collectors.toList());
	}
}
