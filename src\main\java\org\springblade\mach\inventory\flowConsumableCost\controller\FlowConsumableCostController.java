/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.mach.inventory.flowConsumableCost.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.mach.inventory.flowConsumableCost.pojo.entity.FlowConsumableCostEntity;
import org.springblade.mach.inventory.flowConsumableCost.pojo.vo.FlowConsumableCostVO;
import org.springblade.mach.inventory.flowConsumableCost.excel.FlowConsumableCostExcel;
import org.springblade.mach.inventory.flowConsumableCost.wrapper.FlowConsumableCostWrapper;
import org.springblade.mach.inventory.flowConsumableCost.service.IFlowConsumableCostService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 耗材成本配置 控制器
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-flowConsumableCost/flowConsumableCost")
@Tag(name = "耗材成本配置", description = "耗材成本配置接口")
public class FlowConsumableCostController extends BladeController {

	private final IFlowConsumableCostService flowConsumableCostService;

	/**
	 * 耗材成本配置 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入flowConsumableCost")
	public R<FlowConsumableCostVO> detail(FlowConsumableCostEntity flowConsumableCost) {
		FlowConsumableCostEntity detail = flowConsumableCostService.getOne(Condition.getQueryWrapper(flowConsumableCost));
		return R.data(FlowConsumableCostWrapper.build().entityVO(detail));
	}
	/**
	 * 耗材成本配置 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入flowConsumableCost")
	public R<IPage<FlowConsumableCostVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> flowConsumableCost, Query query) {
		IPage<FlowConsumableCostEntity> pages = flowConsumableCostService.page(Condition.getPage(query), Condition.getQueryWrapper(flowConsumableCost, FlowConsumableCostEntity.class));
		return R.data(FlowConsumableCostWrapper.build().pageVO(pages));
	}

	/**
	 * 耗材成本配置 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入flowConsumableCost")
	public R<IPage<FlowConsumableCostVO>> page(FlowConsumableCostVO flowConsumableCost, Query query) {
		IPage<FlowConsumableCostVO> pages = flowConsumableCostService.selectFlowConsumableCostPage(Condition.getPage(query), flowConsumableCost);
		return R.data(pages);
	}

	/**
	 * 耗材成本配置 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入flowConsumableCost")
	public R save(@Valid @RequestBody FlowConsumableCostEntity flowConsumableCost) {
		return R.status(flowConsumableCostService.save(flowConsumableCost));
	}

	/**
	 * 耗材成本配置 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入flowConsumableCost")
	public R update(@Valid @RequestBody FlowConsumableCostEntity flowConsumableCost) {
		return R.status(flowConsumableCostService.updateById(flowConsumableCost));
	}

	/**
	 * 耗材成本配置 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入flowConsumableCost")
	public R submit(@Valid @RequestBody FlowConsumableCostEntity flowConsumableCost) {
		return R.status(flowConsumableCostService.saveOrUpdate(flowConsumableCost));
	}

	/**
	 * 耗材成本配置 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
		return R.status(flowConsumableCostService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-flowConsumableCost")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入flowConsumableCost")
	public void exportFlowConsumableCost(@Parameter(hidden = true) @RequestParam Map<String, Object> flowConsumableCost, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FlowConsumableCostEntity> queryWrapper = Condition.getQueryWrapper(flowConsumableCost, FlowConsumableCostEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(FlowConsumableCost::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(FlowConsumableCostEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FlowConsumableCostExcel> list = flowConsumableCostService.exportFlowConsumableCost(queryWrapper);
		ExcelUtil.export(response, "耗材成本配置数据" + DateUtil.time(), "耗材成本配置数据表", list, FlowConsumableCostExcel.class);
	}

}
