/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.inventory.flowTechSpecDic.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.lang.Double;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
 * 技术参数字典 实体类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@TableName("flow_tech_spec_dic")
@Schema(description = "FlowTechSpecDic对象")
public class FlowTechSpecDicEntity implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 设备系统id
	 */
	@Schema(description = "设备系统id")
	private Long systemId;
	/**
	 * 名称
	 */
	@Schema(description = "名称")
	private String name;
	/**
	 * 编码
	 */
	@Schema(description = "编码")
	private String code;
	/**
	 * 名称展示函数
	 */
	@Schema(description = "名称展示函数")
	private String nameFn;
	/**
	 * 值可选项
	 */
	@Schema(description = "值可选项")
	private String selects;
	/**
	 * 展示类型 select radio
	 */
	@Schema(description = "展示类型")
	private String showType;
	/**
	 * 默认值
	 */
	@Schema(description = "默认值")
	private String valDefault;
	/**
	 * 值展示函数
	 */
	@Schema(description = "值展示函数")
	private String valFn;
	/**
	 * 单位
	 */
	@Schema(description = "单位")
	private String unit;
	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remarks;
	/**
	 * 备注展示函数
	 */
	@Schema(description = "备注展示函数")
	private String remarksFn;
	/**
	 * 等级
	 */
	@Schema(description = "等级")
	private String level;
	/**
	 * 展示函数
	 */
	@Schema(description = "展示函数")
	private String showFn;
	/**
	 * 只读 0否 1是
	 */
	@Schema(description = "只读")
	private Integer readonly;
	/**
	 * 行号
	 */
	@Schema(description = "行号")
	private Integer row;
	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Double sortOrder;

}
