/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayPaymentReal.service.impl;

import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.vo.ApvContractPayPaymentRealVO;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.excel.ApvContractPayPaymentRealExcel;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.mapper.ApvContractPayPaymentRealMapper;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.service.IApvContractPayPaymentRealService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 付款单与款单关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class ApvContractPayPaymentRealServiceImpl extends BaseServiceImpl<ApvContractPayPaymentRealMapper, ApvContractPayPaymentRealEntity> implements IApvContractPayPaymentRealService {

	@Override
	public IPage<ApvContractPayPaymentRealVO> selectApvContractPayPaymentRealPage(IPage<ApvContractPayPaymentRealVO> page, ApvContractPayPaymentRealVO apvContractPayPaymentReal) {
		return page.setRecords(baseMapper.selectApvContractPayPaymentRealPage(page, apvContractPayPaymentReal));
	}


	@Override
	public List<ApvContractPayPaymentRealExcel> exportApvContractPayPaymentReal(Wrapper<ApvContractPayPaymentRealEntity> queryWrapper) {
		List<ApvContractPayPaymentRealExcel> apvContractPayPaymentRealList = baseMapper.exportApvContractPayPaymentReal(queryWrapper);
		//apvContractPayPaymentRealList.forEach(apvContractPayPaymentReal -> {
		//	apvContractPayPaymentReal.setTypeName(DictCache.getValue(DictEnum.YES_NO, ApvContractPayPaymentReal.getType()));
		//});
		return apvContractPayPaymentRealList;
	}

}
