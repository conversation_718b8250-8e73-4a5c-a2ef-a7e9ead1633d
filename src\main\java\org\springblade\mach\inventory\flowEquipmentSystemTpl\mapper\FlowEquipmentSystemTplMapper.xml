<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.inventory.flowEquipmentSystemTpl.mapper.FlowEquipmentSystemTplMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flowEquipmentSystemTplResultMap" type="org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.entity.FlowEquipmentSystemTplEntity">
        <result column="id" property="id"/>
        <result column="system_name" property="systemName"/>
        <result column="system_desc" property="systemDesc"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectFlowEquipmentSystemTplPage" resultMap="flowEquipmentSystemTplResultMap">
        select a.*
        from flow_equipment_system_tpl a
        where a.is_deleted = 0
        <if test="flowEquipmentSystemTpl.systemName != null and flowEquipmentSystemTpl.systemName != ''">
            AND a.system_name LIKE concat('%',#{flowEquipmentSystemTpl.systemName},'%')
        </if>
        order by a.sort_order asc,a.create_time desc
    </select>


    <select id="exportFlowEquipmentSystemTpl" resultType="org.springblade.mach.inventory.flowEquipmentSystemTpl.excel.FlowEquipmentSystemTplExcel">
        SELECT * FROM flow_equipment_system_tpl ${ew.customSqlSegment}
    </select>

    <select id="getEquipmentSystemList"
            resultType="org.springblade.mach.inventory.flowEquipmentSystemTpl.pojo.vo.FlowEquipmentSystemTplVO">
        select
            a.*,
            s.top_level_id as topLevelId,
            s.num
        from flow_top_level_system_sub_tpl s
        left join flow_equipment_system_tpl a on s.system_id=a.id
        where s.is_deleted = 0 and a.is_deleted = 0
        <if test="topLevelId!= null and topLevelId > 0 ">
            AND s.top_level_id = #{topLevelId}
        </if>
        order by s.sort_order asc
    </select>

</mapper>
