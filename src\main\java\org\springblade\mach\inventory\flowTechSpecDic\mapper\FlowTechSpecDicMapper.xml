<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.mach.inventory.flowTechSpecDic.mapper.FlowTechSpecDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flowTechSpecDicResultMap" type="org.springblade.mach.inventory.flowTechSpecDic.pojo.entity.FlowTechSpecDicEntity">
        <result column="id" property="id"/>
        <result column="system_id" property="systemId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="name_fn" property="nameFn"/>
        <result column="selects" property="selects"/>
        <result column="show_type" property="showType"/>
        <result column="val_default" property="valDefault"/>
        <result column="val_fn" property="valFn"/>
        <result column="unit" property="unit"/>
        <result column="remarks" property="remarks"/>
        <result column="remarks_fn" property="remarksFn"/>
        <result column="level" property="level"/>
        <result column="show_fn" property="showFn"/>
        <result column="row" property="row"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectFlowTechSpecDicPage" resultMap="flowTechSpecDicResultMap">
        select
            a.*
        from flow_tech_spec_dic a
        where 1=1
        <if test="flowTechSpecDic.systemId != null">
            and a.system_id = #{flowTechSpecDic.systemId}
        </if>
        order by a.row,a.sort_order
    </select>


    <select id="exportFlowTechSpecDic" resultType="org.springblade.mach.inventory.flowTechSpecDic.excel.FlowTechSpecDicExcel">
        SELECT * FROM flow_tech_spec_dic ${ew.customSqlSegment}
    </select>

    <select id="getMaxRow" resultType="java.lang.Integer">
        select ifnull(max(row),1) from flow_tech_spec_dic where system_id = #{systemId}
    </select>


</mapper>
